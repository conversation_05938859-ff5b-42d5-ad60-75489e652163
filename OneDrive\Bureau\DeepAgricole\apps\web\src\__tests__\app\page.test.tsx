import React from 'react';
import { render, screen } from '@testing-library/react';
import HomePage from '@/app/page';

// Mock the sections components
jest.mock('@/components/sections/hero-section', () => ({
  HeroSection: () => <div data-testid="hero-section">Hero Section</div>,
}));

jest.mock('@/components/sections/features-section', () => ({
  FeaturesSection: () => <div data-testid="features-section">Features Section</div>,
}));

jest.mock('@/components/sections/stats-section', () => ({
  StatsSection: () => <div data-testid="stats-section">Stats Section</div>,
}));

jest.mock('@/components/sections/testimonials-section', () => ({
  TestimonialsSection: () => <div data-testid="testimonials-section">Testimonials Section</div>,
}));

jest.mock('@/components/sections/cta-section', () => ({
  CTASection: () => <div data-testid="cta-section">CTA Section</div>,
}));

describe('HomePage', () => {
  it('should render the main navigation', () => {
    render(<HomePage />);
    
    // Check for logo and brand name
    expect(screen.getByText('AIgricole')).toBeInTheDocument();
    
    // Check for navigation links
    expect(screen.getByText('Fonctionnalités')).toBeInTheDocument();
    expect(screen.getByText('Solutions')).toBeInTheDocument();
    expect(screen.getByText('Tarifs')).toBeInTheDocument();
    expect(screen.getByText('Contact')).toBeInTheDocument();
    
    // Check for auth buttons
    expect(screen.getByText('Connexion')).toBeInTheDocument();
    expect(screen.getByText('Commencer')).toBeInTheDocument();
  });

  it('should render all main sections', () => {
    render(<HomePage />);
    
    // Check that all sections are rendered
    expect(screen.getByTestId('hero-section')).toBeInTheDocument();
    expect(screen.getByTestId('features-section')).toBeInTheDocument();
    expect(screen.getByTestId('stats-section')).toBeInTheDocument();
    expect(screen.getByTestId('testimonials-section')).toBeInTheDocument();
    expect(screen.getByTestId('cta-section')).toBeInTheDocument();
  });

  it('should render the features overview section', () => {
    render(<HomePage />);
    
    // Check for section heading
    expect(screen.getByText('Révolutionnez votre agriculture')).toBeInTheDocument();
    
    // Check for feature cards
    expect(screen.getByText('IA Avancée')).toBeInTheDocument();
    expect(screen.getByText('Imagerie Satellite')).toBeInTheDocument();
    expect(screen.getByText('IoT Intelligent')).toBeInTheDocument();
    expect(screen.getByText('Offline-First')).toBeInTheDocument();
  });

  it('should render the solutions section', () => {
    render(<HomePage />);
    
    // Check for solutions heading
    expect(screen.getByText('Solutions adaptées à votre exploitation')).toBeInTheDocument();
    
    // Check for solution types
    expect(screen.getByText('Grandes Cultures')).toBeInTheDocument();
    expect(screen.getByText('Maraîchage')).toBeInTheDocument();
    expect(screen.getByText('Élevage')).toBeInTheDocument();
    
    // Check for solution descriptions
    expect(screen.getByText('Céréales, oléagineux, protéagineux')).toBeInTheDocument();
    expect(screen.getByText('Légumes, fruits, cultures spécialisées')).toBeInTheDocument();
    expect(screen.getByText('Bovins, ovins, volailles')).toBeInTheDocument();
  });

  it('should render the footer', () => {
    render(<HomePage />);
    
    // Check for footer content
    expect(screen.getByText('© 2024 AIgricole. Tous droits réservés.')).toBeInTheDocument();
    
    // Check for footer links
    expect(screen.getByText('Confidentialité')).toBeInTheDocument();
    expect(screen.getByText('Conditions')).toBeInTheDocument();
    expect(screen.getByText('Cookies')).toBeInTheDocument();
    
    // Check for footer sections
    expect(screen.getByText('Produit')).toBeInTheDocument();
    expect(screen.getByText('Support')).toBeInTheDocument();
    expect(screen.getByText('Entreprise')).toBeInTheDocument();
  });

  it('should have proper navigation links', () => {
    render(<HomePage />);
    
    // Check for anchor links
    const featuresLink = screen.getByRole('link', { name: /fonctionnalités/i });
    expect(featuresLink).toHaveAttribute('href', '#features');
    
    const solutionsLink = screen.getByRole('link', { name: /solutions/i });
    expect(solutionsLink).toHaveAttribute('href', '#solutions');
  });

  it('should have proper CTA buttons', () => {
    render(<HomePage />);
    
    // Check for main CTA buttons
    const registerButton = screen.getByRole('link', { name: /commencer/i });
    expect(registerButton).toHaveAttribute('href', '/auth/register');
    
    const loginButton = screen.getByRole('link', { name: /connexion/i });
    expect(loginButton).toHaveAttribute('href', '/auth/login');
  });

  it('should render feature benefits', () => {
    render(<HomePage />);
    
    // Check for feature benefits in cards
    expect(screen.getByText('Prédiction de rendement par parcelle')).toBeInTheDocument();
    expect(screen.getByText('Irrigation de précision automatisée')).toBeInTheDocument();
    expect(screen.getByText('Surveillance santé du cheptel')).toBeInTheDocument();
  });

  it('should have proper semantic structure', () => {
    render(<HomePage />);
    
    // Check for main semantic elements
    expect(screen.getByRole('banner')).toBeInTheDocument(); // header
    expect(screen.getByRole('main')).toBeInTheDocument(); // main
    expect(screen.getByRole('contentinfo')).toBeInTheDocument(); // footer
    
    // Check for navigation
    expect(screen.getByRole('navigation')).toBeInTheDocument();
  });

  it('should render with proper accessibility', () => {
    render(<HomePage />);
    
    // Check for headings hierarchy
    const mainHeading = screen.getByRole('heading', { level: 1 });
    expect(mainHeading).toBeInTheDocument();
    
    // Check for section headings
    const sectionHeadings = screen.getAllByRole('heading', { level: 2 });
    expect(sectionHeadings.length).toBeGreaterThan(0);
  });

  it('should handle responsive design classes', () => {
    render(<HomePage />);
    
    // Check for responsive classes in the container
    const container = screen.getByRole('main');
    expect(container).toHaveClass('flex-1');
  });

  it('should render popular badge on maraîchage solution', () => {
    render(<HomePage />);
    
    // Check for popular badge
    expect(screen.getByText('Populaire')).toBeInTheDocument();
  });

  it('should render trust indicators', () => {
    render(<HomePage />);
    
    // Check for trust indicator text
    expect(screen.getByText(/plus de 10,000 agriculteurs/i)).toBeInTheDocument();
  });
});
