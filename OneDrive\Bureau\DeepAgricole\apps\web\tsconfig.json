{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/utils/*": ["./src/utils/*"], "@/types/*": ["./src/types/*"], "@/hooks/*": ["./src/hooks/*"], "@/services/*": ["./src/services/*"], "@/app/*": ["./src/app/*"], "@aigricole/database": ["../../packages/database/src"], "@aigricole/shared-ui": ["../../packages/shared-ui/src"], "@aigricole/ai-models": ["../../packages/ai-models/src"], "@aigricole/iot-sdk": ["../../packages/iot-sdk/src"], "@aigricole/offline-sync": ["../../packages/offline-sync/src"]}, "forceConsistentCasingInFileNames": true, "noUncheckedIndexedAccess": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next", "dist", "coverage"]}