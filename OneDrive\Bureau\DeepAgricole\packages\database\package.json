{"name": "@aigricole/database", "version": "1.0.0", "description": "AIgricole Database Package - Prisma ORM with PostgreSQL for agriculture data", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "test": "jest", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:coverage": "jest --coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:seed": "tsx src/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "setup": "npm run db:generate && npm run db:push && npm run db:seed"}, "dependencies": {"@prisma/client": "^5.7.0", "bcryptjs": "^2.4.3", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.8", "@types/node": "^20.10.0", "jest": "^29.7.0", "prisma": "^5.7.0", "ts-jest": "^29.1.1", "tsx": "^4.6.0", "typescript": "^5.3.2"}, "keywords": ["agriculture", "database", "prisma", "postgresql", "orm", "farming", "crops", "iot", "sensors"], "author": "AIgricole Team", "license": "MIT", "publishConfig": {"access": "restricted"}}