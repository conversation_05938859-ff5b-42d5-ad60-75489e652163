{"name": "aigricole-frontend", "version": "1.0.0", "description": "AIgricole Frontend - Modern Agricultural Web Application", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "analyze": "cross-env ANALYZE=true next build", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"next": "^14.2.0", "react": "^18.3.0", "react-dom": "^18.3.0", "typescript": "^5.4.0", "@types/node": "^20.12.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "clsx": "^2.1.0", "tailwind-merge": "^2.3.0", "class-variance-authority": "^0.7.0", "@radix-ui/react-accordion": "^1.1.0", "@radix-ui/react-alert-dialog": "^1.0.0", "@radix-ui/react-avatar": "^1.0.0", "@radix-ui/react-checkbox": "^1.0.0", "@radix-ui/react-dialog": "^1.0.0", "@radix-ui/react-dropdown-menu": "^2.0.0", "@radix-ui/react-label": "^2.0.0", "@radix-ui/react-popover": "^1.0.0", "@radix-ui/react-progress": "^1.0.0", "@radix-ui/react-radio-group": "^1.1.0", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.0", "@radix-ui/react-slider": "^1.1.0", "@radix-ui/react-switch": "^1.0.0", "@radix-ui/react-tabs": "^1.0.0", "@radix-ui/react-toast": "^1.1.0", "@radix-ui/react-tooltip": "^1.0.0", "@radix-ui/react-slot": "^1.0.0", "lucide-react": "^0.378.0", "react-hook-form": "^7.51.0", "@hookform/resolvers": "^3.3.0", "zod": "^3.23.0", "zustand": "^4.5.0", "@tanstack/react-query": "^5.32.0", "@tanstack/react-query-devtools": "^5.32.0", "axios": "^1.6.0", "leaflet": "^1.9.0", "react-leaflet": "^4.2.0", "@types/leaflet": "^1.9.0", "recharts": "^2.12.0", "d3": "^7.9.0", "@types/d3": "^7.4.0", "framer-motion": "^11.1.0", "react-intersection-observer": "^9.10.0", "date-fns": "^3.6.0", "react-day-picker": "^8.10.0", "next-themes": "^0.3.0", "next-pwa": "^5.6.0", "workbox-webpack-plugin": "^7.0.0", "react-dropzone": "^14.2.0", "react-image-crop": "^11.0.0", "socket.io-client": "^4.7.0", "mqtt": "^5.6.0", "i18next": "^23.11.0", "react-i18next": "^14.1.0", "i18next-browser-languagedetector": "^7.2.0", "react-speech-kit": "^3.0.0", "web-speech-api": "^0.0.1", "jspdf": "^2.5.0", "html2canvas": "^1.4.0", "xlsx": "^0.18.0"}, "devDependencies": {"@next/bundle-analyzer": "^14.2.0", "@next/eslint-config-next": "^14.2.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "prettier": "^3.2.0", "prettier-plugin-tailwindcss": "^0.5.0", "@testing-library/react": "^15.0.0", "@testing-library/jest-dom": "^6.4.0", "@testing-library/user-event": "^14.5.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@playwright/test": "^1.43.0", "@storybook/react": "^8.0.0", "@storybook/nextjs": "^8.0.0", "@storybook/addon-essentials": "^8.0.0", "@storybook/addon-interactions": "^8.0.0", "@storybook/addon-links": "^8.0.0", "@storybook/blocks": "^8.0.0", "@storybook/testing-library": "^0.2.0", "storybook": "^8.0.0", "cross-env": "^7.0.0", "husky": "^9.0.0", "lint-staged": "^15.2.0", "@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}