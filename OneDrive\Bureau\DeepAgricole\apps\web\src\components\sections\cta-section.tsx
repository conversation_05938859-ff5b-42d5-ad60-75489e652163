'use client';

import * as React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ArrowRight, CheckCircle, Calendar, Phone, Mail } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

const benefits = [
  'Essai gratuit de 30 jours',
  'Configuration personnalisée',
  'Formation incluse',
  'Support technique dédié',
  'Aucun engagement',
  'Résultats garantis ou remboursé',
];

const ctaOptions = [
  {
    icon: ArrowRight,
    title: 'Commencer maintenant',
    description: 'Créez votre compte et commencez votre essai gratuit immédiatement',
    action: 'Essai gratuit',
    href: '/auth/register',
    primary: true,
  },
  {
    icon: Calendar,
    title: 'Demander une démo',
    description: 'Planifiez une démonstration personnalisée avec nos experts',
    action: 'Réserver une démo',
    href: '/demo',
    primary: false,
  },
  {
    icon: Phone,
    title: '<PERSON><PERSON><PERSON> à un expert',
    description: 'Discutez de vos besoins avec notre équipe commerciale',
    action: 'Nous contacter',
    href: '/contact',
    primary: false,
  },
];

export function CTASection() {
  return (
    <section className="py-24 bg-gradient-to-br from-primary to-primary/80 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-crop-pattern opacity-10" />
      
      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse" />
      <div className="absolute bottom-20 right-20 w-24 h-24 bg-white/10 rounded-full blur-xl animate-pulse delay-1000" />

      <div className="container relative">
        <div className="max-w-4xl mx-auto text-center">
          {/* Main CTA */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Prêt à révolutionner votre agriculture ?
            </h2>
            <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto">
              Rejoignez des milliers d'agriculteurs qui ont déjà transformé leur exploitation 
              avec AIgricole. Commencez dès aujourd'hui et voyez la différence.
            </p>

            {/* Benefits List */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-12 max-w-4xl mx-auto">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={benefit}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex items-center space-x-3 text-left"
                >
                  <CheckCircle className="w-5 h-5 text-green-300 flex-shrink-0" />
                  <span className="text-white/90">{benefit}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* CTA Options */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {ctaOptions.map((option, index) => (
              <motion.div
                key={option.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className={`h-full transition-all duration-300 hover:scale-105 ${
                  option.primary 
                    ? 'bg-white text-gray-900 shadow-2xl ring-4 ring-white/20' 
                    : 'bg-white/10 backdrop-blur-sm text-white border-white/20'
                }`}>
                  <CardContent className="p-8 text-center">
                    <div className={`w-16 h-16 mx-auto mb-6 rounded-2xl flex items-center justify-center ${
                      option.primary ? 'bg-primary/10' : 'bg-white/20'
                    }`}>
                      <option.icon className={`w-8 h-8 ${
                        option.primary ? 'text-primary' : 'text-white'
                      }`} />
                    </div>
                    
                    <h3 className="text-xl font-bold mb-3">{option.title}</h3>
                    <p className={`mb-6 ${
                      option.primary ? 'text-gray-600' : 'text-white/80'
                    }`}>
                      {option.description}
                    </p>
                    
                    <Button
                      size="lg"
                      variant={option.primary ? 'default' : 'outline'}
                      className={`w-full ${
                        option.primary 
                          ? 'bg-primary hover:bg-primary/90 text-white' 
                          : 'border-white/30 text-white hover:bg-white/10'
                      }`}
                      asChild
                    >
                      <Link href={option.href}>
                        {option.action}
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-8"
          >
            <h3 className="text-2xl font-bold mb-6">
              Besoin d'aide pour choisir ?
            </h3>
            <p className="text-white/90 mb-8 max-w-2xl mx-auto">
              Notre équipe d'experts agricoles est là pour vous accompagner dans votre 
              transformation digitale. Contactez-nous pour un conseil personnalisé.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-2xl mx-auto">
              <div className="text-center">
                <Phone className="w-8 h-8 mx-auto mb-3 text-white/80" />
                <div className="font-semibold mb-1">Appelez-nous</div>
                <div className="text-white/80">+33 1 23 45 67 89</div>
                <div className="text-sm text-white/60">Lun-Ven 9h-18h</div>
              </div>
              
              <div className="text-center">
                <Mail className="w-8 h-8 mx-auto mb-3 text-white/80" />
                <div className="font-semibold mb-1">Écrivez-nous</div>
                <div className="text-white/80"><EMAIL></div>
                <div className="text-sm text-white/60">Réponse sous 24h</div>
              </div>
            </div>
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mt-16 text-center"
          >
            <div className="flex flex-wrap justify-center items-center gap-8 text-white/60">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5" />
                <span>Données sécurisées</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5" />
                <span>Conformité RGPD</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5" />
                <span>Support 24/7</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5" />
                <span>Satisfaction garantie</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
