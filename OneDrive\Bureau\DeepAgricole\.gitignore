# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
/node_modules
/.pnp
.pnp.js
**/node_modules/
**/npm-debug.log*
**/yarn-debug.log*
**/yarn-error.log*
**/lerna-debug.log*

# Testing
/coverage
**/__tests__/coverage/
**/test-results/
**/performance-results/
**/.nyc_output

# Next.js
/.next/
/out/
**/.next/
**/out/

# Production build
/build
/dist
**/build/
**/dist/

# Misc
.DS_Store
*.pem
**/.DS_Store
**/*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env
.env*.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Turbo
.turbo

# IDE
.vscode/settings.json
.idea/
*.swp
*.swo

# OS
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Agriculture-specific ignores
# AI Models (large files)
**/models/*.h5
**/models/*.pb
**/models/*.onnx
**/models/*.tflite
**/models/*.pkl
**/models/*.joblib
**/models/*.bin
**/models/*.safetensors

# Training data (large datasets)
**/training-datasets/*.csv
**/training-datasets/*.json
**/training-datasets/*.parquet
**/training-datasets/images/
**/training-datasets/satellite/
**/training-datasets/drone/

# Satellite imagery cache
**/satellite-cache/
**/imagery-cache/

# IoT data logs
**/iot-logs/
**/sensor-data/
**/device-logs/

# Weather data cache
**/weather-cache/
**/forecast-cache/

# Temporary processing files
**/temp-processing/
**/image-processing-temp/
**/ml-temp/

# Database
*.db
*.sqlite
*.sqlite3
**/*.db
**/*.sqlite
**/*.sqlite3

# Redis dumps
dump.rdb

# Docker
.dockerignore
docker-compose.override.yml

# Kubernetes secrets
**/k8s/*secret*.yml
**/k8s/*secret*.yaml

# Terraform
**/*.tfstate
**/*.tfstate.*
**/.terraform/
**/.terraform.lock.hcl

# Logs
logs
*.log
**/*.log
**/logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/
