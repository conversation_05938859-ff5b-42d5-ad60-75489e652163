'use client';

import * as React from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, Users, MapPin, Award } from 'lucide-react';

const stats = [
  {
    icon: Users,
    value: '10,000+',
    label: 'Agriculteurs actifs',
    description: 'Font confiance à AIgricole',
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
  },
  {
    icon: MapPin,
    value: '500,000',
    label: 'Hectares surveillés',
    description: 'Partout en France',
    color: 'text-green-600',
    bgColor: 'bg-green-50',
  },
  {
    icon: TrendingUp,
    value: '+25%',
    label: 'Augmentation moyenne',
    description: 'Des rendements',
    color: 'text-purple-600',
    bgColor: 'bg-purple-50',
  },
  {
    icon: Award,
    value: '98%',
    label: 'Satisfaction client',
    description: 'Recommandent AIgricole',
    color: 'text-orange-600',
    bgColor: 'bg-orange-50',
  },
];

const achievements = [
  {
    title: 'Prix Innovation Agricole 2024',
    organization: 'Salon International de l\'Agriculture',
    year: '2024',
  },
  {
    title: 'Startup AgTech de l\'Année',
    organization: 'French Tech',
    year: '2023',
  },
  {
    title: 'Certification ISO 27001',
    organization: 'Sécurité des données',
    year: '2023',
  },
  {
    title: 'Partenaire Microsoft AI',
    organization: 'Microsoft for Startups',
    year: '2022',
  },
];

const impactMetrics = [
  {
    metric: '2.5M tonnes',
    description: 'CO₂ économisées grâce à l\'optimisation',
    icon: '🌱',
  },
  {
    metric: '40%',
    description: 'Réduction moyenne des intrants chimiques',
    icon: '🧪',
  },
  {
    metric: '30%',
    description: 'Économie d\'eau d\'irrigation',
    icon: '💧',
  },
  {
    metric: '15M€',
    description: 'Économies générées pour nos clients',
    icon: '💰',
  },
];

export function StatsSection() {
  return (
    <section className="py-24 bg-gray-50">
      <div className="container">
        {/* Main Stats */}
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl font-bold tracking-tight sm:text-4xl mb-4"
          >
            Des résultats qui parlent d'eux-mêmes
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-xl text-muted-foreground max-w-3xl mx-auto"
          >
            Rejoignez des milliers d'agriculteurs qui ont déjà transformé leur exploitation 
            avec AIgricole
          </motion.p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className={`inline-flex items-center justify-center w-16 h-16 ${stat.bgColor} rounded-2xl mb-4`}>
                <stat.icon className={`w-8 h-8 ${stat.color}`} />
              </div>
              <div className="text-4xl font-bold text-gray-900 mb-2">
                {stat.value}
              </div>
              <div className="text-lg font-semibold text-gray-700 mb-1">
                {stat.label}
              </div>
              <div className="text-sm text-muted-foreground">
                {stat.description}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Impact Metrics */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-white rounded-2xl p-8 shadow-lg mb-16"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold mb-4">Impact Environnemental & Économique</h3>
            <p className="text-muted-foreground">
              AIgricole contribue à une agriculture plus durable et rentable
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {impactMetrics.map((impact, index) => (
              <motion.div
                key={impact.metric}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center p-6 rounded-xl bg-gradient-to-br from-green-50 to-blue-50"
              >
                <div className="text-4xl mb-3">{impact.icon}</div>
                <div className="text-3xl font-bold text-primary mb-2">
                  {impact.metric}
                </div>
                <div className="text-sm text-muted-foreground">
                  {impact.description}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Achievements */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h3 className="text-2xl font-bold mb-8">Reconnaissances & Certifications</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {achievements.map((achievement, index) => (
              <motion.div
                key={achievement.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-lg p-6 shadow-sm border border-gray-100"
              >
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Award className="w-6 h-6 text-primary" />
                </div>
                <div className="font-semibold text-gray-900 mb-2">
                  {achievement.title}
                </div>
                <div className="text-sm text-muted-foreground mb-1">
                  {achievement.organization}
                </div>
                <div className="text-xs text-primary font-medium">
                  {achievement.year}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Customer Success Quote */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-primary/5 rounded-2xl p-8 max-w-4xl mx-auto">
            <div className="text-6xl text-primary/20 mb-4">"</div>
            <blockquote className="text-xl text-gray-700 mb-6 italic">
              Depuis que j'utilise AIgricole, j'ai augmenté mes rendements de 30% tout en 
              réduisant mes coûts d'intrants de 25%. L'IA me permet de prendre des décisions 
              éclairées et d'optimiser chaque parcelle individuellement.
            </blockquote>
            <div className="flex items-center justify-center space-x-4">
              <div className="w-12 h-12 bg-gray-300 rounded-full" />
              <div className="text-left">
                <div className="font-semibold text-gray-900">Jean-Pierre Dubois</div>
                <div className="text-sm text-muted-foreground">
                  Céréalier, 450 hectares - Beauce
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
