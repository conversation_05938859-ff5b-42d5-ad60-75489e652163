import { chromium, FullConfig } from '@playwright/test';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

async function globalSetup(config: FullConfig) {
  console.log('🔧 Setting up E2E test environment...');

  // Check if we're in CI environment
  const isCI = !!process.env.CI;
  
  // Setup test database
  await setupTestDatabase();
  
  // Setup test data
  await setupTestData();
  
  // Warm up the application if not in CI
  if (!isCI) {
    await warmupApplication(config);
  }
  
  console.log('✅ E2E test environment setup complete!');
}

async function setupTestDatabase() {
  console.log('🗄️  Setting up test database...');
  
  try {
    // Set test environment
    process.env.NODE_ENV = 'test';
    process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 
      'postgresql://aigricole:aigricole_dev_password@localhost:5432/aigricole_test?schema=public';
    
    // Create test database if it doesn't exist
    try {
      execSync('createdb aigricole_test', { stdio: 'ignore' });
    } catch (error) {
      // Database might already exist, that's okay
    }
    
    // Run migrations on test database
    execSync('npx prisma migrate deploy', {
      stdio: 'inherit',
      env: { ...process.env, DATABASE_URL: process.env.TEST_DATABASE_URL }
    });
    
    console.log('✅ Test database setup complete');
  } catch (error) {
    console.error('❌ Failed to setup test database:', error);
    throw error;
  }
}

async function setupTestData() {
  console.log('📊 Setting up test data...');
  
  try {
    // Run test data seeding
    execSync('npm run db:seed:test', {
      stdio: 'inherit',
      env: { ...process.env, NODE_ENV: 'test' }
    });
    
    console.log('✅ Test data setup complete');
  } catch (error) {
    console.warn('⚠️  Test data seeding failed, continuing without seed data');
  }
}

async function warmupApplication(config: FullConfig) {
  console.log('🔥 Warming up application...');
  
  const baseURL = config.projects[0]?.use?.baseURL || 'http://localhost:3000';
  
  try {
    // Launch a browser to warm up the application
    const browser = await chromium.launch();
    const page = await browser.newPage();
    
    // Visit main pages to warm up
    const pagesToWarmup = [
      '/',
      '/auth/login',
      '/auth/register',
    ];
    
    for (const pagePath of pagesToWarmup) {
      try {
        await page.goto(`${baseURL}${pagePath}`, { 
          waitUntil: 'networkidle',
          timeout: 30000 
        });
        console.log(`✅ Warmed up: ${pagePath}`);
      } catch (error) {
        console.warn(`⚠️  Failed to warm up ${pagePath}:`, error.message);
      }
    }
    
    await browser.close();
    console.log('✅ Application warmup complete');
  } catch (error) {
    console.warn('⚠️  Application warmup failed:', error.message);
  }
}

// Create test user accounts for authentication tests
async function createTestUsers() {
  console.log('👥 Creating test users...');
  
  const testUsers = [
    {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      firstName: 'Test',
      lastName: 'Farmer',
      role: 'FARMER',
    },
    {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      firstName: 'Test',
      lastName: 'Admin',
      role: 'ADMIN',
    },
    {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      firstName: 'Test',
      lastName: 'Agronomist',
      role: 'AGRONOMIST',
    },
  ];
  
  // Save test users to a file for use in tests
  const testDataPath = path.join(__dirname, 'fixtures', 'test-users.json');
  fs.mkdirSync(path.dirname(testDataPath), { recursive: true });
  fs.writeFileSync(testDataPath, JSON.stringify(testUsers, null, 2));
  
  console.log('✅ Test users created');
}

// Setup test environment variables
function setupTestEnvironment() {
  console.log('🌍 Setting up test environment variables...');
  
  // Set test-specific environment variables
  process.env.NODE_ENV = 'test';
  process.env.NEXTAUTH_URL = 'http://localhost:3000';
  process.env.NEXTAUTH_SECRET = 'test-secret-key';
  
  // Disable external services in tests
  process.env.DISABLE_ANALYTICS = 'true';
  process.env.DISABLE_SENTRY = 'true';
  process.env.DISABLE_EXTERNAL_APIS = 'true';
  
  console.log('✅ Test environment variables set');
}

// Clean up any existing test artifacts
function cleanupTestArtifacts() {
  console.log('🧹 Cleaning up test artifacts...');
  
  const artifactPaths = [
    'test-results',
    'playwright-report',
    'coverage',
  ];
  
  artifactPaths.forEach(artifactPath => {
    if (fs.existsSync(artifactPath)) {
      fs.rmSync(artifactPath, { recursive: true, force: true });
    }
  });
  
  console.log('✅ Test artifacts cleaned up');
}

// Main setup function
async function main(config: FullConfig) {
  try {
    setupTestEnvironment();
    cleanupTestArtifacts();
    await createTestUsers();
    await globalSetup(config);
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    process.exit(1);
  }
}

export default main;
