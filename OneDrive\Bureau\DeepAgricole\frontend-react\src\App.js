/**
 * AIgricole Simple App Component
 * Simplified version for initial testing
 */

import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box, Typography, Card, CardContent, Button } from '@mui/material';

// Simple Dashboard Component
const Dashboard = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" component="h1" sx={{ mb: 3, fontWeight: 600 }}>
      🌱 AIgricole Dashboard
    </Typography>
    <Card>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Bienvenue sur AIgricole !
        </Typography>
        <Typography variant="body1" sx={{ mb: 2 }}>
          Votre plateforme d'IA agricole est maintenant opérationnelle.
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Backend Python FastAPI : ✅ Actif sur http://localhost:8000
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Frontend React : ✅ Actif sur http://localhost:3000
        </Typography>
      </CardContent>
    </Card>
  </Box>
);

// Simple Login Component
const Login = () => (
  <Box sx={{
    minHeight: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    background: 'linear-gradient(135deg, #2e7d32 0%, #4caf50 100%)'
  }}>
    <Card sx={{ maxWidth: 400, width: '100%' }}>
      <CardContent sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600, mb: 2 }}>
          🌱 AIgricole
        </Typography>
        <Typography variant="body1" sx={{ mb: 3 }}>
          Plateforme d'IA Agricole Avancée
        </Typography>
        <Button
          variant="contained"
          fullWidth
          size="large"
          onClick={() => {
            localStorage.setItem('aigricole_user', 'demo');
            window.location.href = '/dashboard';
          }}
        >
          Connexion Demo
        </Button>
      </CardContent>
    </Card>
  </Box>
);

// Simple Protected Route
const ProtectedRoute = ({ children }) => {
  const isAuthenticated = localStorage.getItem('aigricole_user');
  return isAuthenticated ? children : <Navigate to="/login" replace />;
};

// Simple Layout
const Layout = () => (
  <Box>
    <Dashboard />
  </Box>
);

// Create theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#2e7d32', // Green for agriculture
      light: '#60ad5e',
      dark: '#005005',
    },
    secondary: {
      main: '#ff9800', // Orange for alerts/warnings
      light: '#ffb74d',
      dark: '#f57c00',
    },
    background: {
      default: '#f5f5f5',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 600,
    },
    h2: {
      fontWeight: 600,
    },
    h3: {
      fontWeight: 600,
    },
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 600,
        },
      },
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
          <Routes>
            {/* Public routes */}
            <Route path="/login" element={<Login />} />
            
            {/* Protected routes */}
            <Route path="/" element={
              <ProtectedRoute>
                <Layout />
              </ProtectedRoute>
            }>
              <Route index element={<Navigate to="/dashboard" replace />} />
              <Route path="dashboard" element={<Dashboard />} />
            </Route>
            
            {/* Catch all route */}
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </Box>
      </Router>
    </ThemeProvider>
  );
}

export default App;
