import { PrismaClient } from './generated';
import type { SearchFiltersInput, SortOptionsInput } from './validators';

// ================================
// PAGINATION UTILITIES
// ================================

export interface PaginationOptions {
  page: number;
  limit: number;
}

export interface PaginationResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export async function paginate<T>(
  query: () => Promise<T[]>,
  countQuery: () => Promise<number>,
  options: PaginationOptions
): Promise<PaginationResult<T>> {
  const { page, limit } = options;
  const skip = (page - 1) * limit;

  const [data, total] = await Promise.all([
    query(),
    countQuery(),
  ]);

  const totalPages = Math.ceil(total / limit);

  return {
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
}

// ================================
// SEARCH UTILITIES
// ================================

export function buildSearchWhere(filters: SearchFiltersInput) {
  const where: any = {};

  if (filters.query) {
    where.OR = [
      { name: { contains: filters.query, mode: 'insensitive' } },
      { description: { contains: filters.query, mode: 'insensitive' } },
    ];
  }

  if (filters.farmId) {
    where.farmId = filters.farmId;
  }

  if (filters.fieldId) {
    where.fieldId = filters.fieldId;
  }

  if (filters.cropType) {
    where.cropType = filters.cropType;
  }

  if (filters.status) {
    where.status = filters.status;
  }

  if (filters.severity) {
    where.severity = filters.severity;
  }

  if (filters.dateFrom || filters.dateTo) {
    where.createdAt = {};
    if (filters.dateFrom) {
      where.createdAt.gte = filters.dateFrom;
    }
    if (filters.dateTo) {
      where.createdAt.lte = filters.dateTo;
    }
  }

  return where;
}

export function buildOrderBy(sort: SortOptionsInput) {
  return {
    [sort.field]: sort.direction,
  };
}

// ================================
// GEOSPATIAL UTILITIES
// ================================

export interface BoundingBox {
  north: number;
  south: number;
  east: number;
  west: number;
}

export function calculateBoundingBox(coordinates: number[][][]): BoundingBox {
  const points = coordinates[0]; // First ring of polygon
  let north = -90, south = 90, east = -180, west = 180;

  for (const [lng, lat] of points) {
    if (lat > north) north = lat;
    if (lat < south) south = lat;
    if (lng > east) east = lng;
    if (lng < west) west = lng;
  }

  return { north, south, east, west };
}

export function calculatePolygonArea(coordinates: number[][][]): number {
  // Simplified area calculation using shoelace formula
  const points = coordinates[0];
  let area = 0;

  for (let i = 0; i < points.length - 1; i++) {
    const [x1, y1] = points[i];
    const [x2, y2] = points[i + 1];
    area += x1 * y2 - x2 * y1;
  }

  return Math.abs(area) / 2;
}

export function pointInPolygon(point: [number, number], polygon: number[][][]): boolean {
  const [x, y] = point;
  const vertices = polygon[0];
  let inside = false;

  for (let i = 0, j = vertices.length - 1; i < vertices.length; j = i++) {
    const [xi, yi] = vertices[i];
    const [xj, yj] = vertices[j];

    if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
      inside = !inside;
    }
  }

  return inside;
}

// ================================
// DATE UTILITIES
// ================================

export function getDateRange(period: 'day' | 'week' | 'month' | 'year'): { from: Date; to: Date } {
  const now = new Date();
  const to = new Date(now);
  const from = new Date(now);

  switch (period) {
    case 'day':
      from.setDate(from.getDate() - 1);
      break;
    case 'week':
      from.setDate(from.getDate() - 7);
      break;
    case 'month':
      from.setMonth(from.getMonth() - 1);
      break;
    case 'year':
      from.setFullYear(from.getFullYear() - 1);
      break;
  }

  return { from, to };
}

export function formatDateForDatabase(date: Date): string {
  return date.toISOString();
}

export function parseDate(dateString: string): Date {
  return new Date(dateString);
}

// ================================
// AGRICULTURE CALCULATIONS
// ================================

export function calculateGrowingDegreeDays(
  baseTemp: number,
  maxTemp: number,
  minTemp: number
): number {
  const avgTemp = (maxTemp + minTemp) / 2;
  return Math.max(0, avgTemp - baseTemp);
}

export function calculateEvapotranspiration(
  temperature: number,
  humidity: number,
  windSpeed: number,
  solarRadiation: number
): number {
  // Simplified Penman-Monteith equation
  const delta = 4098 * (0.6108 * Math.exp(17.27 * temperature / (temperature + 237.3))) / Math.pow(temperature + 237.3, 2);
  const gamma = 0.665; // psychrometric constant
  const u2 = windSpeed * 4.87 / Math.log(67.8 * 10 - 5.42); // wind speed at 2m height
  
  const et0 = (0.408 * delta * solarRadiation + gamma * 900 / (temperature + 273) * u2 * (0.01 * (100 - humidity))) /
              (delta + gamma * (1 + 0.34 * u2));
  
  return Math.max(0, et0);
}

export function calculateIrrigationNeed(
  currentMoisture: number,
  targetMoisture: number,
  fieldCapacity: number,
  area: number // in hectares
): number {
  const moistureDeficit = Math.max(0, targetMoisture - currentMoisture);
  const waterDepth = (moistureDeficit / 100) * fieldCapacity; // mm
  const waterVolume = waterDepth * area * 10; // liters (1mm over 1ha = 10,000L)
  
  return waterVolume;
}

export function calculateNutrientIndex(nitrogen: number, phosphorus: number, potassium: number): {
  nIndex: 'low' | 'medium' | 'high';
  pIndex: 'low' | 'medium' | 'high';
  kIndex: 'low' | 'medium' | 'high';
} {
  return {
    nIndex: nitrogen < 20 ? 'low' : nitrogen < 40 ? 'medium' : 'high',
    pIndex: phosphorus < 15 ? 'low' : phosphorus < 30 ? 'medium' : 'high',
    kIndex: potassium < 100 ? 'low' : potassium < 200 ? 'medium' : 'high',
  };
}

// ================================
// VALIDATION UTILITIES
// ================================

export function validateCoordinates(lat: number, lng: number): boolean {
  return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
}

export function validatePolygon(coordinates: number[][][]): boolean {
  if (!coordinates || !coordinates[0] || coordinates[0].length < 4) {
    return false;
  }

  const ring = coordinates[0];
  const first = ring[0];
  const last = ring[ring.length - 1];

  // Check if polygon is closed
  if (first[0] !== last[0] || first[1] !== last[1]) {
    return false;
  }

  // Check if all coordinates are valid
  return ring.every(([lng, lat]) => validateCoordinates(lat, lng));
}

// ================================
// ERROR HANDLING UTILITIES
// ================================

export class DatabaseError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'DatabaseError';
  }
}

export function handlePrismaError(error: any): DatabaseError {
  if (error.code === 'P2002') {
    return new DatabaseError('Unique constraint violation', 'UNIQUE_CONSTRAINT', error.meta);
  }
  
  if (error.code === 'P2025') {
    return new DatabaseError('Record not found', 'NOT_FOUND', error.meta);
  }
  
  if (error.code === 'P2003') {
    return new DatabaseError('Foreign key constraint violation', 'FOREIGN_KEY_CONSTRAINT', error.meta);
  }
  
  return new DatabaseError(error.message || 'Database error', 'UNKNOWN', error);
}

// ================================
// PERFORMANCE UTILITIES
// ================================

export async function withTransaction<T>(
  prisma: PrismaClient,
  callback: (tx: PrismaClient) => Promise<T>
): Promise<T> {
  return prisma.$transaction(callback);
}

export function createBatchProcessor<T, R>(
  batchSize: number,
  processor: (batch: T[]) => Promise<R[]>
) {
  return async (items: T[]): Promise<R[]> => {
    const results: R[] = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchResults = await processor(batch);
      results.push(...batchResults);
    }
    
    return results;
  };
}
