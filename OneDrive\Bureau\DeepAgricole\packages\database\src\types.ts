import type {
  User,
  Farm,
  Field,
  Crop,
  Sensor,
  SensorReading,
  WeatherData,
  CropHealthRecord,
  SoilAnalysis,
  Treatment,
  IrrigationZone,
  IrrigationEvent,
  Harvest,
  Alert,
  Prediction,
  AIModel,
} from './generated';

// ================================
// EXTENDED TYPES WITH RELATIONS
// ================================

export type UserWithFarms = User & {
  farms: Farm[];
};

export type FarmWithFields = Farm & {
  fields: Field[];
  sensors: Sensor[];
  owner: User;
};

export type FieldWithCrops = Field & {
  crops: Crop[];
  soilAnalyses: SoilAnalysis[];
  irrigationZones: IrrigationZone[];
  farm: Farm;
};

export type CropWithHealth = Crop & {
  healthRecords: CropHealthRecord[];
  treatments: Treatment[];
  harvests: Harvest[];
  field: Field;
};

export type SensorWithReadings = Sensor & {
  readings: SensorReading[];
  farm: Farm;
};

// ================================
// AGRICULTURE-SPECIFIC TYPES
// ================================

export interface GeoCoordinates {
  latitude: number;
  longitude: number;
}

export interface GeoPolygon {
  type: 'Polygon';
  coordinates: number[][][]; // [[[lng, lat], [lng, lat], ...]]
}

export interface WeatherConditions {
  temperature: number;
  humidity: number;
  windSpeed?: number;
  rainfall?: number;
  pressure?: number;
}

export interface SoilConditions {
  moisture: number;
  temperature: number;
  ph: number;
  nutrients: {
    nitrogen: number;
    phosphorus: number;
    potassium: number;
  };
}

export interface CropGrowthStage {
  stage: string;
  description: string;
  daysFromPlanting: number;
  expectedDuration: number;
}

export interface YieldPrediction {
  expectedYield: number; // tons per hectare
  confidence: number; // 0-1
  factors: {
    weather: number;
    soil: number;
    crop: number;
    management: number;
  };
  recommendations: string[];
}

export interface DiseaseDetection {
  disease: string;
  confidence: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  affectedArea: number; // percentage
  recommendations: string[];
  treatmentOptions: TreatmentOption[];
}

export interface TreatmentOption {
  product: string;
  dosage: number;
  unit: string;
  cost: number;
  effectiveness: number; // 0-1
  environmentalImpact: 'low' | 'medium' | 'high';
}

export interface IrrigationRecommendation {
  shouldIrrigate: boolean;
  duration: number; // minutes
  waterAmount: number; // liters
  urgency: 'low' | 'medium' | 'high';
  reasoning: string[];
  costBenefit: {
    cost: number;
    benefit: number;
    roi: number;
  };
}

// ================================
// API RESPONSE TYPES
// ================================

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface DashboardData {
  farms: FarmWithFields[];
  alerts: Alert[];
  weatherSummary: WeatherConditions;
  cropsSummary: {
    total: number;
    byStatus: Record<string, number>;
    healthScore: number;
  };
  sensorsSummary: {
    total: number;
    active: number;
    offline: number;
    batteryLow: number;
  };
  recentActivity: ActivityItem[];
}

export interface ActivityItem {
  id: string;
  type: 'harvest' | 'treatment' | 'irrigation' | 'alert' | 'sensor';
  title: string;
  description: string;
  timestamp: Date;
  severity?: 'low' | 'medium' | 'high';
  farmId: string;
  fieldId?: string;
  cropId?: string;
}

// ================================
// SEARCH AND FILTER TYPES
// ================================

export interface SearchFilters {
  query?: string;
  farmId?: string;
  fieldId?: string;
  cropType?: string;
  dateFrom?: Date;
  dateTo?: Date;
  status?: string;
  severity?: string;
}

export interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

// ================================
// ANALYTICS TYPES
// ================================

export interface AnalyticsData {
  period: 'day' | 'week' | 'month' | 'year';
  metrics: {
    yield: TimeSeriesData[];
    weather: TimeSeriesData[];
    irrigation: TimeSeriesData[];
    costs: TimeSeriesData[];
    revenue: TimeSeriesData[];
  };
  comparisons: {
    previousPeriod: number;
    benchmark: number;
    target: number;
  };
}

export interface TimeSeriesData {
  timestamp: Date;
  value: number;
  label?: string;
}

// ================================
// EXPORT TYPES
// ================================

export interface ExportOptions {
  format: 'csv' | 'xlsx' | 'pdf' | 'json';
  dateRange: {
    from: Date;
    to: Date;
  };
  fields: string[];
  filters?: SearchFilters;
}

// ================================
// NOTIFICATION TYPES
// ================================

export interface NotificationPreferences {
  email: boolean;
  sms: boolean;
  push: boolean;
  webhook?: string;
  alertTypes: string[];
  quietHours: {
    start: string; // HH:MM
    end: string; // HH:MM
  };
}

// ================================
// INTEGRATION TYPES
// ================================

export interface WeatherAPIResponse {
  current: WeatherConditions;
  forecast: WeatherConditions[];
  alerts: WeatherAlert[];
}

export interface WeatherAlert {
  type: string;
  severity: string;
  description: string;
  startTime: Date;
  endTime: Date;
}

export interface SatelliteImagery {
  date: Date;
  cloudCover: number;
  resolution: number; // meters per pixel
  bands: {
    red: string; // URL
    green: string;
    blue: string;
    nir: string; // Near-infrared
  };
  indices: {
    ndvi: number[];
    ndwi: number[];
    savi: number[];
  };
}
