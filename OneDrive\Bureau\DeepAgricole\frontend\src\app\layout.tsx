import type { Metada<PERSON>, Viewport } from 'next';
import { Inter, JetBrains_Mono } from 'next/font/google';
import { ThemeProvider } from '@/components/providers/theme-provider';
import { QueryProvider } from '@/components/providers/query-provider';
import { AuthProvider } from '@/components/providers/auth-provider';
import { ToastProvider } from '@/components/providers/toast-provider';
import { I18nProvider } from '@/components/providers/i18n-provider';
import { OfflineProvider } from '@/components/providers/offline-provider';
import { cn } from '@/lib/utils';
import '@/styles/globals.css';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
  display: 'swap',
});

const jetbrainsMono = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-mono',
  display: 'swap',
});

export const metadata: Metadata = {
  title: {
    default: 'AIgricole - Suite d\'IA Agricole Complète',
    template: '%s | AIgricole',
  },
  description: 'Révolutionnez votre agriculture avec l\'intelligence artificielle. Gestion complète des cultures, IoT intelligent, et prédictions précises pour optimiser vos rendements.',
  keywords: [
    'agriculture',
    'intelligence artificielle',
    'IoT agricole',
    'gestion des cultures',
    'agriculture de précision',
    'capteurs agricoles',
    'prédiction de rendement',
    'surveillance des cultures',
    'irrigation intelligente',
    'analyse du sol',
    'vision par ordinateur',
    'agriculture connectée',
    'smart farming',
    'agtech',
    'precision agriculture',
  ],
  authors: [
    {
      name: 'AIgricole Team',
      url: 'https://aigricole.com',
    },
  ],
  creator: 'AIgricole',
  publisher: 'AIgricole',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'https://app.aigricole.com'),
  alternates: {
    canonical: '/',
    languages: {
      'fr-FR': '/fr',
      'en-US': '/en',
      'ar-MA': '/ar',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'fr_FR',
    url: process.env.NEXT_PUBLIC_APP_URL || 'https://app.aigricole.com',
    title: 'AIgricole - Suite d\'IA Agricole Complète',
    description: 'Révolutionnez votre agriculture avec l\'intelligence artificielle',
    siteName: 'AIgricole',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'AIgricole - Agriculture Intelligente',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AIgricole - Suite d\'IA Agricole Complète',
    description: 'Révolutionnez votre agriculture avec l\'intelligence artificielle',
    images: ['/twitter-image.jpg'],
    creator: '@aigricole',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  manifest: '/manifest.json',
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      {
        rel: 'mask-icon',
        url: '/safari-pinned-tab.svg',
        color: '#22c55e',
      },
    ],
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'AIgricole',
    startupImage: [
      {
        url: '/apple-splash-2048-2732.jpg',
        media: '(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2)',
      },
      {
        url: '/apple-splash-1668-2224.jpg',
        media: '(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2)',
      },
      {
        url: '/apple-splash-1536-2048.jpg',
        media: '(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2)',
      },
    ],
  },
  category: 'agriculture',
  classification: 'Business',
  other: {
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'msapplication-TileColor': '#22c55e',
    'msapplication-config': '/browserconfig.xml',
    'theme-color': '#22c55e',
  },
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#22c55e' },
    { media: '(prefers-color-scheme: dark)', color: '#16a34a' },
  ],
  colorScheme: 'light dark',
  viewportFit: 'cover',
};

interface RootLayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="fr" suppressHydrationWarning>
      <head>
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href={process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'} />
        
        {/* DNS prefetch for performance */}
        <link rel="dns-prefetch" href="//api.aigricole.com" />
        <link rel="dns-prefetch" href="//cdn.aigricole.com" />
        <link rel="dns-prefetch" href="//tiles.openstreetmap.org" />
        
        {/* Security headers */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-Frame-Options" content="DENY" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
        <meta httpEquiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
        
        {/* Performance hints */}
        <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
        
        {/* PWA meta tags */}
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="AIgricole" />
        <meta name="application-name" content="AIgricole" />
        <meta name="msapplication-TileColor" content="#22c55e" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
        
        {/* Structured data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'WebApplication',
              name: 'AIgricole',
              description: 'Suite d\'IA Agricole Complète',
              url: process.env.NEXT_PUBLIC_APP_URL || 'https://app.aigricole.com',
              applicationCategory: 'BusinessApplication',
              operatingSystem: 'Web',
              browserRequirements: 'Requires JavaScript. Requires HTML5.',
              softwareVersion: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
              offers: {
                '@type': 'Offer',
                category: 'Agriculture Technology',
                price: '0',
                priceCurrency: 'EUR',
              },
              author: {
                '@type': 'Organization',
                name: 'AIgricole',
                url: 'https://aigricole.com',
              },
              featureList: [
                'Intelligence Artificielle Agricole',
                'IoT et Capteurs Connectés',
                'Imagerie Satellitaire',
                'Prédictions de Rendement',
                'Gestion des Cultures',
                'Irrigation Intelligente',
              ],
            }),
          }}
        />
      </head>
      <body
        className={cn(
          'min-h-screen bg-background font-sans antialiased safe-top safe-bottom',
          inter.variable,
          jetbrainsMono.variable
        )}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <I18nProvider>
            <QueryProvider>
              <AuthProvider>
                <OfflineProvider>
                  <div className="relative flex min-h-screen flex-col">
                    <div className="flex-1">
                      {children}
                    </div>
                  </div>
                  <ToastProvider />
                </OfflineProvider>
              </AuthProvider>
            </QueryProvider>
          </I18nProvider>
        </ThemeProvider>
        
        {/* Service Worker Registration */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }
            `,
          }}
        />
        
        {/* Analytics */}
        {process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_GA_ID && (
          <>
            <script
              async
              src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`}
            />
            <script
              dangerouslySetInnerHTML={{
                __html: `
                  window.dataLayer = window.dataLayer || [];
                  function gtag(){dataLayer.push(arguments);}
                  gtag('js', new Date());
                  gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}', {
                    page_title: document.title,
                    page_location: window.location.href,
                    custom_map: {
                      'custom_parameter_1': 'user_type',
                      'custom_parameter_2': 'farm_size',
                    }
                  });
                `,
              }}
            />
          </>
        )}
      </body>
    </html>
  );
}
