import { z } from 'zod';

// ================================
// BASE VALIDATORS
// ================================

export const coordinatesSchema = z.object({
  latitude: z.number().min(-90).max(90),
  longitude: z.number().min(-180).max(180),
});

export const geoPolygonSchema = z.object({
  type: z.literal('Polygon'),
  coordinates: z.array(z.array(z.array(z.number().array().length(2)))),
});

export const paginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
});

// ================================
// USER VALIDATORS
// ================================

export const createUserSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8).max(100),
  firstName: z.string().min(1).max(50),
  lastName: z.string().min(1).max(50),
  phone: z.string().optional(),
  role: z.enum(['FARMER', 'AGRONOMIST', 'COOPERATIVE', 'ADMIN', 'TECHNICIAN']).default('FARMER'),
});

export const updateUserSchema = createUserSchema.partial().omit({ password: true });

export const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(1),
});

// ================================
// FARM VALIDATORS
// ================================

export const createFarmSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  address: z.string().min(1).max(200),
  latitude: z.number().min(-90).max(90),
  longitude: z.number().min(-180).max(180),
  totalArea: z.number().positive(),
  timezone: z.string().default('Europe/Paris'),
});

export const updateFarmSchema = createFarmSchema.partial();

// ================================
// FIELD VALIDATORS
// ================================

export const createFieldSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  area: z.number().positive(),
  soilType: z.enum(['CLAY', 'SANDY', 'LOAMY', 'SILTY', 'PEATY', 'CHALKY']),
  slope: z.number().min(0).max(90).optional(),
  elevation: z.number().optional(),
  coordinates: geoPolygonSchema,
  farmId: z.string().cuid(),
});

export const updateFieldSchema = createFieldSchema.partial().omit({ farmId: true });

// ================================
// CROP VALIDATORS
// ================================

export const createCropSchema = z.object({
  name: z.string().min(1).max(100),
  variety: z.string().min(1).max(100),
  cropType: z.enum(['CEREALS', 'VEGETABLES', 'FRUITS', 'LEGUMES', 'OILSEEDS', 'FORAGE', 'INDUSTRIAL']),
  plantingDate: z.date(),
  expectedHarvestDate: z.date().optional(),
  expectedYield: z.number().positive().optional(),
  fieldId: z.string().cuid(),
});

export const updateCropSchema = createCropSchema.partial().omit({ fieldId: true });

// ================================
// SENSOR VALIDATORS
// ================================

export const createSensorSchema = z.object({
  name: z.string().min(1).max(100),
  type: z.enum([
    'SOIL_MOISTURE',
    'SOIL_TEMPERATURE',
    'SOIL_PH',
    'AIR_TEMPERATURE',
    'AIR_HUMIDITY',
    'WIND_SPEED',
    'WIND_DIRECTION',
    'RAINFALL',
    'SOLAR_RADIATION',
    'LEAF_WETNESS',
    'PRESSURE',
    'CO2',
    'MULTI_SENSOR',
  ]),
  model: z.string().max(100).optional(),
  serialNumber: z.string().max(100).optional(),
  latitude: z.number().min(-90).max(90),
  longitude: z.number().min(-180).max(180),
  measurementInterval: z.number().int().min(60).default(300), // minimum 1 minute
  farmId: z.string().cuid(),
});

export const updateSensorSchema = createSensorSchema.partial().omit({ farmId: true });

export const sensorReadingSchema = z.object({
  timestamp: z.date(),
  value: z.number(),
  unit: z.string().min(1).max(20),
  quality: z.enum(['EXCELLENT', 'GOOD', 'FAIR', 'POOR', 'ERROR']).default('GOOD'),
  sensorId: z.string().cuid(),
});

// ================================
// TREATMENT VALIDATORS
// ================================

export const createTreatmentSchema = z.object({
  type: z.enum(['FERTILIZER', 'PESTICIDE', 'HERBICIDE', 'FUNGICIDE', 'INSECTICIDE', 'IRRIGATION', 'OTHER']),
  product: z.string().min(1).max(100),
  dosage: z.number().positive(),
  unit: z.string().min(1).max(20),
  applicationDate: z.date(),
  method: z.string().max(100).optional(),
  notes: z.string().max(500).optional(),
  cost: z.number().positive().optional(),
  cropId: z.string().cuid(),
});

export const updateTreatmentSchema = createTreatmentSchema.partial().omit({ cropId: true });

// ================================
// SOIL ANALYSIS VALIDATORS
// ================================

export const createSoilAnalysisSchema = z.object({
  date: z.date(),
  depth: z.number().positive(),
  ph: z.number().min(0).max(14),
  organicMatter: z.number().min(0).max(100),
  nitrogen: z.number().min(0),
  phosphorus: z.number().min(0),
  potassium: z.number().min(0),
  calcium: z.number().min(0).optional(),
  magnesium: z.number().min(0).optional(),
  sulfur: z.number().min(0).optional(),
  moisture: z.number().min(0).max(100).optional(),
  temperature: z.number().optional(),
  compaction: z.number().min(0).optional(),
  fieldId: z.string().cuid(),
});

export const updateSoilAnalysisSchema = createSoilAnalysisSchema.partial().omit({ fieldId: true });

// ================================
// IRRIGATION VALIDATORS
// ================================

export const createIrrigationZoneSchema = z.object({
  name: z.string().min(1).max(100),
  area: z.number().positive(),
  soilType: z.enum(['CLAY', 'SANDY', 'LOAMY', 'SILTY', 'PEATY', 'CHALKY']),
  cropType: z.enum(['CEREALS', 'VEGETABLES', 'FRUITS', 'LEGUMES', 'OILSEEDS', 'FORAGE', 'INDUSTRIAL']).optional(),
  systemType: z.enum(['DRIP', 'SPRINKLER', 'FLOOD', 'FURROW', 'PIVOT', 'MICRO_SPRAY']),
  flowRate: z.number().positive().optional(),
  efficiency: z.number().min(0).max(100).optional(),
  fieldId: z.string().cuid(),
});

export const updateIrrigationZoneSchema = createIrrigationZoneSchema.partial().omit({ fieldId: true });

export const createIrrigationScheduleSchema = z.object({
  name: z.string().min(1).max(100),
  startDate: z.date(),
  endDate: z.date().optional(),
  frequency: z.enum(['DAILY', 'EVERY_OTHER_DAY', 'WEEKLY', 'BI_WEEKLY', 'MONTHLY', 'ON_DEMAND', 'SENSOR_BASED']),
  duration: z.number().int().min(1).max(1440), // max 24 hours
  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/), // HH:MM format
  minSoilMoisture: z.number().min(0).max(100).optional(),
  maxTemperature: z.number().optional(),
  zoneId: z.string().cuid(),
});

export const updateIrrigationScheduleSchema = createIrrigationScheduleSchema.partial().omit({ zoneId: true });

// ================================
// HARVEST VALIDATORS
// ================================

export const createHarvestSchema = z.object({
  date: z.date(),
  quantity: z.number().positive(),
  quality: z.enum(['EXCELLENT', 'GOOD', 'AVERAGE', 'POOR', 'REJECTED']),
  moistureContent: z.number().min(0).max(100).optional(),
  storageLocation: z.string().max(100).optional(),
  notes: z.string().max(500).optional(),
  pricePerTon: z.number().positive().optional(),
  totalValue: z.number().positive().optional(),
  costs: z.number().positive().optional(),
  cropId: z.string().cuid(),
});

export const updateHarvestSchema = createHarvestSchema.partial().omit({ cropId: true });

// ================================
// ALERT VALIDATORS
// ================================

export const createAlertSchema = z.object({
  title: z.string().min(1).max(100),
  message: z.string().min(1).max(500),
  type: z.enum([
    'WEATHER_WARNING',
    'DISEASE_DETECTED',
    'PEST_ALERT',
    'IRRIGATION_NEEDED',
    'HARVEST_READY',
    'SENSOR_MALFUNCTION',
    'SYSTEM_ERROR',
    'MAINTENANCE_DUE',
  ]),
  severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
  data: z.record(z.any()).optional(),
  threshold: z.number().optional(),
  actualValue: z.number().optional(),
  userId: z.string().cuid(),
});

export const updateAlertSchema = z.object({
  status: z.enum(['ACTIVE', 'ACKNOWLEDGED', 'RESOLVED', 'DISMISSED']),
});

// ================================
// SEARCH AND FILTER VALIDATORS
// ================================

export const searchFiltersSchema = z.object({
  query: z.string().optional(),
  farmId: z.string().cuid().optional(),
  fieldId: z.string().cuid().optional(),
  cropType: z.string().optional(),
  dateFrom: z.date().optional(),
  dateTo: z.date().optional(),
  status: z.string().optional(),
  severity: z.string().optional(),
});

export const sortOptionsSchema = z.object({
  field: z.string().min(1),
  direction: z.enum(['asc', 'desc']).default('desc'),
});

// ================================
// EXPORT VALIDATORS
// ================================

export const exportOptionsSchema = z.object({
  format: z.enum(['csv', 'xlsx', 'pdf', 'json']),
  dateRange: z.object({
    from: z.date(),
    to: z.date(),
  }),
  fields: z.array(z.string()),
  filters: searchFiltersSchema.optional(),
});

// ================================
// TYPE INFERENCE
// ================================

export type CreateUserInput = z.infer<typeof createUserSchema>;
export type UpdateUserInput = z.infer<typeof updateUserSchema>;
export type LoginInput = z.infer<typeof loginSchema>;
export type CreateFarmInput = z.infer<typeof createFarmSchema>;
export type UpdateFarmInput = z.infer<typeof updateFarmSchema>;
export type CreateFieldInput = z.infer<typeof createFieldSchema>;
export type UpdateFieldInput = z.infer<typeof updateFieldSchema>;
export type CreateCropInput = z.infer<typeof createCropSchema>;
export type UpdateCropInput = z.infer<typeof updateCropSchema>;
export type CreateSensorInput = z.infer<typeof createSensorSchema>;
export type UpdateSensorInput = z.infer<typeof updateSensorSchema>;
export type SensorReadingInput = z.infer<typeof sensorReadingSchema>;
export type CreateTreatmentInput = z.infer<typeof createTreatmentSchema>;
export type UpdateTreatmentInput = z.infer<typeof updateTreatmentSchema>;
export type CreateSoilAnalysisInput = z.infer<typeof createSoilAnalysisSchema>;
export type UpdateSoilAnalysisInput = z.infer<typeof updateSoilAnalysisSchema>;
export type CreateIrrigationZoneInput = z.infer<typeof createIrrigationZoneSchema>;
export type UpdateIrrigationZoneInput = z.infer<typeof updateIrrigationZoneSchema>;
export type CreateIrrigationScheduleInput = z.infer<typeof createIrrigationScheduleSchema>;
export type UpdateIrrigationScheduleInput = z.infer<typeof updateIrrigationScheduleSchema>;
export type CreateHarvestInput = z.infer<typeof createHarvestSchema>;
export type UpdateHarvestInput = z.infer<typeof updateHarvestSchema>;
export type CreateAlertInput = z.infer<typeof createAlertSchema>;
export type UpdateAlertInput = z.infer<typeof updateAlertSchema>;
export type SearchFiltersInput = z.infer<typeof searchFiltersSchema>;
export type SortOptionsInput = z.infer<typeof sortOptionsSchema>;
export type ExportOptionsInput = z.infer<typeof exportOptionsSchema>;
