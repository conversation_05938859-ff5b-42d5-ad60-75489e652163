import {
  createUserSchema,
  updateUserSchema,
  loginSchema,
  createFarmSchema,
  createFieldSchema,
  createCropSchema,
  createSensorSchema,
  sensorReadingSchema,
  createTreatmentSchema,
  createSoilAnalysisSchema,
  createIrrigationZoneSchema,
  createHarvestSchema,
  createAlertSchema,
  searchFiltersSchema,
  exportOptionsSchema,
} from '../src/validators';

describe('User Validators', () => {
  describe('createUserSchema', () => {
    it('should validate correct user data', () => {
      const validUser = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe',
        phone: '+33123456789',
        role: 'FARMER' as const,
      };

      const result = createUserSchema.safeParse(validUser);
      expect(result.success).toBe(true);
    });

    it('should reject invalid email', () => {
      const invalidUser = {
        email: 'invalid-email',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe',
      };

      const result = createUserSchema.safeParse(invalidUser);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('email');
      }
    });

    it('should reject short password', () => {
      const invalidUser = {
        email: '<EMAIL>',
        password: '123',
        firstName: 'John',
        lastName: 'Doe',
      };

      const result = createUserSchema.safeParse(invalidUser);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('password');
      }
    });

    it('should use default role', () => {
      const userWithoutRole = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe',
      };

      const result = createUserSchema.safeParse(userWithoutRole);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.role).toBe('FARMER');
      }
    });
  });

  describe('loginSchema', () => {
    it('should validate correct login data', () => {
      const validLogin = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const result = loginSchema.safeParse(validLogin);
      expect(result.success).toBe(true);
    });

    it('should reject empty password', () => {
      const invalidLogin = {
        email: '<EMAIL>',
        password: '',
      };

      const result = loginSchema.safeParse(invalidLogin);
      expect(result.success).toBe(false);
    });
  });
});

describe('Farm Validators', () => {
  describe('createFarmSchema', () => {
    it('should validate correct farm data', () => {
      const validFarm = {
        name: 'Test Farm',
        description: 'A test farm',
        address: '123 Farm Road, Test City',
        latitude: 46.0,
        longitude: 2.0,
        totalArea: 100.5,
        timezone: 'Europe/Paris',
      };

      const result = createFarmSchema.safeParse(validFarm);
      expect(result.success).toBe(true);
    });

    it('should reject invalid coordinates', () => {
      const invalidFarm = {
        name: 'Test Farm',
        address: '123 Farm Road, Test City',
        latitude: 91, // Invalid latitude
        longitude: 2.0,
        totalArea: 100.5,
      };

      const result = createFarmSchema.safeParse(invalidFarm);
      expect(result.success).toBe(false);
    });

    it('should reject negative area', () => {
      const invalidFarm = {
        name: 'Test Farm',
        address: '123 Farm Road, Test City',
        latitude: 46.0,
        longitude: 2.0,
        totalArea: -10, // Negative area
      };

      const result = createFarmSchema.safeParse(invalidFarm);
      expect(result.success).toBe(false);
    });
  });
});

describe('Field Validators', () => {
  describe('createFieldSchema', () => {
    it('should validate correct field data', () => {
      const validField = {
        name: 'Test Field',
        description: 'A test field',
        area: 25.5,
        soilType: 'LOAMY' as const,
        slope: 2.5,
        elevation: 120,
        coordinates: {
          type: 'Polygon' as const,
          coordinates: [[[2.0, 46.0], [2.01, 46.0], [2.01, 46.01], [2.0, 46.01], [2.0, 46.0]]]
        },
        farmId: 'cltest123456789',
      };

      const result = createFieldSchema.safeParse(validField);
      expect(result.success).toBe(true);
    });

    it('should reject invalid soil type', () => {
      const invalidField = {
        name: 'Test Field',
        area: 25.5,
        soilType: 'INVALID_SOIL',
        coordinates: {
          type: 'Polygon' as const,
          coordinates: [[[2.0, 46.0], [2.01, 46.0], [2.01, 46.01], [2.0, 46.01], [2.0, 46.0]]]
        },
        farmId: 'cltest123456789',
      };

      const result = createFieldSchema.safeParse(invalidField);
      expect(result.success).toBe(false);
    });

    it('should reject invalid slope', () => {
      const invalidField = {
        name: 'Test Field',
        area: 25.5,
        soilType: 'LOAMY' as const,
        slope: 95, // Invalid slope > 90 degrees
        coordinates: {
          type: 'Polygon' as const,
          coordinates: [[[2.0, 46.0], [2.01, 46.0], [2.01, 46.01], [2.0, 46.01], [2.0, 46.0]]]
        },
        farmId: 'cltest123456789',
      };

      const result = createFieldSchema.safeParse(invalidField);
      expect(result.success).toBe(false);
    });
  });
});

describe('Crop Validators', () => {
  describe('createCropSchema', () => {
    it('should validate correct crop data', () => {
      const validCrop = {
        name: 'Test Crop',
        variety: 'Test Variety',
        cropType: 'CEREALS' as const,
        plantingDate: new Date('2024-04-15'),
        expectedHarvestDate: new Date('2024-09-15'),
        expectedYield: 8.5,
        fieldId: 'cltest123456789',
      };

      const result = createCropSchema.safeParse(validCrop);
      expect(result.success).toBe(true);
    });

    it('should reject invalid crop type', () => {
      const invalidCrop = {
        name: 'Test Crop',
        variety: 'Test Variety',
        cropType: 'INVALID_CROP',
        plantingDate: new Date('2024-04-15'),
        fieldId: 'cltest123456789',
      };

      const result = createCropSchema.safeParse(invalidCrop);
      expect(result.success).toBe(false);
    });

    it('should reject negative yield', () => {
      const invalidCrop = {
        name: 'Test Crop',
        variety: 'Test Variety',
        cropType: 'CEREALS' as const,
        plantingDate: new Date('2024-04-15'),
        expectedYield: -5, // Negative yield
        fieldId: 'cltest123456789',
      };

      const result = createCropSchema.safeParse(invalidCrop);
      expect(result.success).toBe(false);
    });
  });
});

describe('Sensor Validators', () => {
  describe('createSensorSchema', () => {
    it('should validate correct sensor data', () => {
      const validSensor = {
        name: 'Test Sensor',
        type: 'SOIL_MOISTURE' as const,
        model: 'Test Model',
        serialNumber: 'TEST123',
        latitude: 46.0,
        longitude: 2.0,
        measurementInterval: 300,
        farmId: 'cltest123456789',
      };

      const result = createSensorSchema.safeParse(validSensor);
      expect(result.success).toBe(true);
    });

    it('should reject invalid sensor type', () => {
      const invalidSensor = {
        name: 'Test Sensor',
        type: 'INVALID_SENSOR',
        latitude: 46.0,
        longitude: 2.0,
        farmId: 'cltest123456789',
      };

      const result = createSensorSchema.safeParse(invalidSensor);
      expect(result.success).toBe(false);
    });

    it('should reject short measurement interval', () => {
      const invalidSensor = {
        name: 'Test Sensor',
        type: 'SOIL_MOISTURE' as const,
        latitude: 46.0,
        longitude: 2.0,
        measurementInterval: 30, // Too short (< 60 seconds)
        farmId: 'cltest123456789',
      };

      const result = createSensorSchema.safeParse(invalidSensor);
      expect(result.success).toBe(false);
    });
  });

  describe('sensorReadingSchema', () => {
    it('should validate correct sensor reading', () => {
      const validReading = {
        timestamp: new Date(),
        value: 45.5,
        unit: '%',
        quality: 'GOOD' as const,
        sensorId: 'cltest123456789',
      };

      const result = sensorReadingSchema.safeParse(validReading);
      expect(result.success).toBe(true);
    });

    it('should use default quality', () => {
      const readingWithoutQuality = {
        timestamp: new Date(),
        value: 45.5,
        unit: '%',
        sensorId: 'cltest123456789',
      };

      const result = sensorReadingSchema.safeParse(readingWithoutQuality);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.quality).toBe('GOOD');
      }
    });
  });
});

describe('Treatment Validators', () => {
  describe('createTreatmentSchema', () => {
    it('should validate correct treatment data', () => {
      const validTreatment = {
        type: 'FERTILIZER' as const,
        product: 'Test Fertilizer',
        dosage: 50,
        unit: 'kg/ha',
        applicationDate: new Date(),
        method: 'Spray',
        notes: 'Test notes',
        cost: 100,
        cropId: 'cltest123456789',
      };

      const result = createTreatmentSchema.safeParse(validTreatment);
      expect(result.success).toBe(true);
    });

    it('should reject negative dosage', () => {
      const invalidTreatment = {
        type: 'FERTILIZER' as const,
        product: 'Test Fertilizer',
        dosage: -10, // Negative dosage
        unit: 'kg/ha',
        applicationDate: new Date(),
        cropId: 'cltest123456789',
      };

      const result = createTreatmentSchema.safeParse(invalidTreatment);
      expect(result.success).toBe(false);
    });
  });
});

describe('Soil Analysis Validators', () => {
  describe('createSoilAnalysisSchema', () => {
    it('should validate correct soil analysis data', () => {
      const validAnalysis = {
        date: new Date(),
        depth: 20,
        ph: 6.5,
        organicMatter: 3.2,
        nitrogen: 25,
        phosphorus: 18,
        potassium: 120,
        moisture: 35,
        temperature: 18,
        fieldId: 'cltest123456789',
      };

      const result = createSoilAnalysisSchema.safeParse(validAnalysis);
      expect(result.success).toBe(true);
    });

    it('should reject invalid pH', () => {
      const invalidAnalysis = {
        date: new Date(),
        depth: 20,
        ph: 15, // Invalid pH > 14
        organicMatter: 3.2,
        nitrogen: 25,
        phosphorus: 18,
        potassium: 120,
        fieldId: 'cltest123456789',
      };

      const result = createSoilAnalysisSchema.safeParse(invalidAnalysis);
      expect(result.success).toBe(false);
    });

    it('should reject invalid moisture percentage', () => {
      const invalidAnalysis = {
        date: new Date(),
        depth: 20,
        ph: 6.5,
        organicMatter: 3.2,
        nitrogen: 25,
        phosphorus: 18,
        potassium: 120,
        moisture: 150, // Invalid moisture > 100%
        fieldId: 'cltest123456789',
      };

      const result = createSoilAnalysisSchema.safeParse(invalidAnalysis);
      expect(result.success).toBe(false);
    });
  });
});

describe('Search and Filter Validators', () => {
  describe('searchFiltersSchema', () => {
    it('should validate correct search filters', () => {
      const validFilters = {
        query: 'test search',
        farmId: 'cltest123456789',
        cropType: 'CEREALS',
        dateFrom: new Date('2024-01-01'),
        dateTo: new Date('2024-12-31'),
        status: 'ACTIVE',
        severity: 'HIGH',
      };

      const result = searchFiltersSchema.safeParse(validFilters);
      expect(result.success).toBe(true);
    });

    it('should allow empty filters', () => {
      const emptyFilters = {};

      const result = searchFiltersSchema.safeParse(emptyFilters);
      expect(result.success).toBe(true);
    });
  });

  describe('exportOptionsSchema', () => {
    it('should validate correct export options', () => {
      const validOptions = {
        format: 'csv' as const,
        dateRange: {
          from: new Date('2024-01-01'),
          to: new Date('2024-12-31'),
        },
        fields: ['name', 'type', 'value'],
        filters: {
          query: 'test',
        },
      };

      const result = exportOptionsSchema.safeParse(validOptions);
      expect(result.success).toBe(true);
    });

    it('should reject invalid format', () => {
      const invalidOptions = {
        format: 'invalid_format',
        dateRange: {
          from: new Date('2024-01-01'),
          to: new Date('2024-12-31'),
        },
        fields: ['name'],
      };

      const result = exportOptionsSchema.safeParse(invalidOptions);
      expect(result.success).toBe(false);
    });
  });
});
