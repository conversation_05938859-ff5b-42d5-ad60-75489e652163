import { PrismaClient } from './generated';
import { execSync } from 'child_process';
import { randomBytes } from 'crypto';

// Test database instance
let testPrisma: PrismaClient | null = null;

// ================================
// TEST DATABASE SETUP
// ================================

export async function createTestDatabase(): Promise<PrismaClient> {
  if (testPrisma) {
    return testPrisma;
  }

  // Generate unique test database name
  const testDbName = `aigricole_test_${randomBytes(8).toString('hex')}`;
  const testDatabaseUrl = process.env.TEST_DATABASE_URL?.replace('aigricole_test', testDbName) || 
    `postgresql://aigricole:aigricole_dev_password@localhost:5432/${testDbName}?schema=public`;

  try {
    // Create test database
    execSync(`createdb ${testDbName}`, { stdio: 'ignore' });
    
    // Set environment variable for Prisma
    process.env.DATABASE_URL = testDatabaseUrl;
    
    // Create Prisma client for test database
    testPrisma = new PrismaClient({
      datasources: {
        db: {
          url: testDatabaseUrl,
        },
      },
    });

    // Run migrations
    execSync('npx prisma migrate deploy', { 
      stdio: 'ignore',
      env: { ...process.env, DATABASE_URL: testDatabaseUrl }
    });

    console.log(`✅ Test database created: ${testDbName}`);
    return testPrisma;
  } catch (error) {
    console.error('❌ Failed to create test database:', error);
    throw error;
  }
}

export async function cleanupTestDatabase(): Promise<void> {
  if (!testPrisma) {
    return;
  }

  try {
    // Get database name from connection
    const dbName = testPrisma.$queryRaw`SELECT current_database()` as Promise<[{ current_database: string }]>;
    const [{ current_database }] = await dbName;

    // Disconnect Prisma client
    await testPrisma.$disconnect();
    testPrisma = null;

    // Drop test database
    execSync(`dropdb ${current_database}`, { stdio: 'ignore' });
    
    console.log(`✅ Test database cleaned up: ${current_database}`);
  } catch (error) {
    console.error('❌ Failed to cleanup test database:', error);
  }
}

// ================================
// TEST DATA FACTORIES
// ================================

export const testDataFactory = {
  user: (overrides: any = {}) => ({
    email: `test-${randomBytes(4).toString('hex')}@example.com`,
    password: 'hashedpassword123',
    firstName: 'Test',
    lastName: 'User',
    phone: '+33123456789',
    role: 'FARMER' as const,
    ...overrides,
  }),

  farm: (ownerId: string, overrides: any = {}) => ({
    name: `Test Farm ${randomBytes(4).toString('hex')}`,
    description: 'Test farm description',
    address: '123 Test Street, Test City, France',
    latitude: 46.0 + Math.random() * 2, // France coordinates
    longitude: 2.0 + Math.random() * 2,
    totalArea: 100 + Math.random() * 200,
    ownerId,
    ...overrides,
  }),

  field: (farmId: string, overrides: any = {}) => ({
    name: `Test Field ${randomBytes(4).toString('hex')}`,
    description: 'Test field description',
    area: 10 + Math.random() * 40,
    soilType: 'LOAMY' as const,
    slope: Math.random() * 5,
    elevation: 100 + Math.random() * 100,
    coordinates: {
      type: 'Polygon' as const,
      coordinates: [[[2.0, 46.0], [2.01, 46.0], [2.01, 46.01], [2.0, 46.01], [2.0, 46.0]]]
    },
    farmId,
    ...overrides,
  }),

  crop: (fieldId: string, overrides: any = {}) => ({
    name: 'Test Crop',
    variety: 'Test Variety',
    cropType: 'CEREALS' as const,
    plantingDate: new Date('2024-04-15'),
    expectedHarvestDate: new Date('2024-09-15'),
    expectedYield: 8.5,
    fieldId,
    ...overrides,
  }),

  sensor: (farmId: string, overrides: any = {}) => ({
    name: `Test Sensor ${randomBytes(4).toString('hex')}`,
    type: 'SOIL_MOISTURE' as const,
    model: 'Test Model',
    serialNumber: `TEST${randomBytes(4).toString('hex').toUpperCase()}`,
    latitude: 46.0 + Math.random() * 2,
    longitude: 2.0 + Math.random() * 2,
    measurementInterval: 300,
    batteryLevel: 80 + Math.random() * 20,
    farmId,
    ...overrides,
  }),

  sensorReading: (sensorId: string, overrides: any = {}) => ({
    timestamp: new Date(),
    value: Math.random() * 100,
    unit: '%',
    quality: 'GOOD' as const,
    sensorId,
    ...overrides,
  }),

  soilAnalysis: (fieldId: string, overrides: any = {}) => ({
    date: new Date(),
    depth: 20,
    ph: 6.5 + Math.random() * 1.5,
    organicMatter: 2 + Math.random() * 3,
    nitrogen: 20 + Math.random() * 30,
    phosphorus: 15 + Math.random() * 25,
    potassium: 100 + Math.random() * 100,
    moisture: 30 + Math.random() * 40,
    temperature: 15 + Math.random() * 10,
    fieldId,
    ...overrides,
  }),

  treatment: (cropId: string, overrides: any = {}) => ({
    type: 'FERTILIZER' as const,
    product: 'Test Fertilizer',
    dosage: 50 + Math.random() * 100,
    unit: 'kg/ha',
    applicationDate: new Date(),
    method: 'Spray application',
    notes: 'Test treatment notes',
    cost: 100 + Math.random() * 200,
    cropId,
    ...overrides,
  }),

  irrigationZone: (fieldId: string, overrides: any = {}) => ({
    name: `Test Irrigation Zone ${randomBytes(4).toString('hex')}`,
    area: 5 + Math.random() * 15,
    soilType: 'LOAMY' as const,
    cropType: 'CEREALS' as const,
    systemType: 'DRIP' as const,
    flowRate: 100 + Math.random() * 200,
    efficiency: 80 + Math.random() * 15,
    fieldId,
    ...overrides,
  }),

  harvest: (cropId: string, overrides: any = {}) => ({
    date: new Date(),
    quantity: 5 + Math.random() * 15,
    quality: 'GOOD' as const,
    moistureContent: 12 + Math.random() * 8,
    storageLocation: 'Test Storage',
    notes: 'Test harvest notes',
    pricePerTon: 200 + Math.random() * 100,
    totalValue: 1000 + Math.random() * 2000,
    costs: 100 + Math.random() * 200,
    cropId,
    ...overrides,
  }),

  alert: (userId: string, overrides: any = {}) => ({
    title: 'Test Alert',
    message: 'This is a test alert message',
    type: 'WEATHER_WARNING' as const,
    severity: 'MEDIUM' as const,
    data: { testData: true },
    threshold: 50,
    actualValue: 75,
    userId,
    ...overrides,
  }),
};

// ================================
// TEST HELPERS
// ================================

export async function createTestUser(prisma: PrismaClient, overrides: any = {}) {
  return prisma.user.create({
    data: testDataFactory.user(overrides),
  });
}

export async function createTestFarm(prisma: PrismaClient, ownerId: string, overrides: any = {}) {
  return prisma.farm.create({
    data: testDataFactory.farm(ownerId, overrides),
  });
}

export async function createTestField(prisma: PrismaClient, farmId: string, overrides: any = {}) {
  return prisma.field.create({
    data: testDataFactory.field(farmId, overrides),
  });
}

export async function createTestCrop(prisma: PrismaClient, fieldId: string, overrides: any = {}) {
  return prisma.crop.create({
    data: testDataFactory.crop(fieldId, overrides),
  });
}

export async function createTestSensor(prisma: PrismaClient, farmId: string, overrides: any = {}) {
  return prisma.sensor.create({
    data: testDataFactory.sensor(farmId, overrides),
  });
}

export async function createCompleteTestSetup(prisma: PrismaClient) {
  // Create a complete test setup with all related entities
  const user = await createTestUser(prisma);
  const farm = await createTestFarm(prisma, user.id);
  const field = await createTestField(prisma, farm.id);
  const crop = await createTestCrop(prisma, field.id);
  const sensor = await createTestSensor(prisma, farm.id);

  return {
    user,
    farm,
    field,
    crop,
    sensor,
  };
}

// ================================
// TEST ASSERTIONS
// ================================

export const testAssertions = {
  async expectUserExists(prisma: PrismaClient, userId: string) {
    const user = await prisma.user.findUnique({ where: { id: userId } });
    if (!user) {
      throw new Error(`Expected user with id ${userId} to exist`);
    }
    return user;
  },

  async expectFarmExists(prisma: PrismaClient, farmId: string) {
    const farm = await prisma.farm.findUnique({ where: { id: farmId } });
    if (!farm) {
      throw new Error(`Expected farm with id ${farmId} to exist`);
    }
    return farm;
  },

  async expectFieldExists(prisma: PrismaClient, fieldId: string) {
    const field = await prisma.field.findUnique({ where: { id: fieldId } });
    if (!field) {
      throw new Error(`Expected field with id ${fieldId} to exist`);
    }
    return field;
  },

  async expectCropExists(prisma: PrismaClient, cropId: string) {
    const crop = await prisma.crop.findUnique({ where: { id: cropId } });
    if (!crop) {
      throw new Error(`Expected crop with id ${cropId} to exist`);
    }
    return crop;
  },

  async expectSensorExists(prisma: PrismaClient, sensorId: string) {
    const sensor = await prisma.sensor.findUnique({ where: { id: sensorId } });
    if (!sensor) {
      throw new Error(`Expected sensor with id ${sensorId} to exist`);
    }
    return sensor;
  },

  expectValidCoordinates(latitude: number, longitude: number) {
    if (latitude < -90 || latitude > 90) {
      throw new Error(`Invalid latitude: ${latitude}`);
    }
    if (longitude < -180 || longitude > 180) {
      throw new Error(`Invalid longitude: ${longitude}`);
    }
  },

  expectValidPolygon(coordinates: any) {
    if (!coordinates || !coordinates.coordinates || !Array.isArray(coordinates.coordinates)) {
      throw new Error('Invalid polygon structure');
    }
    
    const ring = coordinates.coordinates[0];
    if (!Array.isArray(ring) || ring.length < 4) {
      throw new Error('Polygon must have at least 4 points');
    }
    
    // Check if polygon is closed
    const first = ring[0];
    const last = ring[ring.length - 1];
    if (first[0] !== last[0] || first[1] !== last[1]) {
      throw new Error('Polygon must be closed');
    }
  },
};

// ================================
// CLEANUP HELPERS
// ================================

export async function cleanupTestData(prisma: PrismaClient) {
  // Clean up test data in reverse dependency order
  await prisma.alert.deleteMany();
  await prisma.harvest.deleteMany();
  await prisma.irrigationEvent.deleteMany();
  await prisma.irrigationSchedule.deleteMany();
  await prisma.irrigationZone.deleteMany();
  await prisma.treatment.deleteMany();
  await prisma.soilAnalysis.deleteMany();
  await prisma.cropHealthRecord.deleteMany();
  await prisma.weatherData.deleteMany();
  await prisma.weatherStation.deleteMany();
  await prisma.sensorReading.deleteMany();
  await prisma.sensor.deleteMany();
  await prisma.crop.deleteMany();
  await prisma.field.deleteMany();
  await prisma.farm.deleteMany();
  await prisma.userSession.deleteMany();
  await prisma.user.deleteMany();
}

// ================================
// JEST SETUP HELPERS
// ================================

export function setupTestDatabase() {
  let testDb: PrismaClient;

  beforeAll(async () => {
    testDb = await createTestDatabase();
  });

  afterAll(async () => {
    await cleanupTestDatabase();
  });

  beforeEach(async () => {
    await cleanupTestData(testDb);
  });

  return () => testDb;
}
