import { PrismaClient } from './generated';

// Global Prisma client instance
declare global {
  // eslint-disable-next-line no-var
  var __prisma: PrismaClient | undefined;
}

// Create Prisma client with optimized configuration
export const prisma =
  globalThis.__prisma ||
  new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    errorFormat: 'pretty',
  });

// In development, store the client globally to prevent multiple instances
if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma;
}

// Graceful shutdown
process.on('beforeExit', async () => {
  await prisma.$disconnect();
});

// Export all Prisma types
export * from './generated';

// Export custom types and utilities
export * from './types';
export * from './utils';
export * from './validators';

// Export database utilities
export { seedDatabase } from './seed';
export { createTestDatabase, cleanupTestDatabase } from './test-utils';

// Default export
export default prisma;
