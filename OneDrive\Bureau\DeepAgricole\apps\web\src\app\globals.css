@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 142.1 76.2% 36.3%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 210 40% 98%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 98%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 98%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 142.1 76.2% 36.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 142.1 70.6% 45.3%;
    --primary-foreground: 144.9 80.4% 10%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 142.4 71.8% 29.2%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-md;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }

  /* Focus styles */
  .focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }

  /* Selection styles */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }

  /* Print styles */
  @media print {
    .no-print {
      display: none !important;
    }
    
    .print-break-before {
      page-break-before: always;
    }
    
    .print-break-after {
      page-break-after: always;
    }
  }
}

@layer components {
  /* Agriculture-specific components */
  .field-card {
    @apply bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow;
  }

  .crop-status-healthy {
    @apply bg-green-100 text-green-800 border border-green-200;
  }

  .crop-status-warning {
    @apply bg-yellow-100 text-yellow-800 border border-yellow-200;
  }

  .crop-status-danger {
    @apply bg-red-100 text-red-800 border border-red-200;
  }

  .sensor-card {
    @apply bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-200;
  }

  .weather-card {
    @apply bg-gradient-to-br from-blue-50 to-sky-50 border border-blue-200 rounded-lg p-4;
  }

  .metric-card {
    @apply bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow;
  }

  .alert-card {
    @apply border-l-4 p-4 rounded-r-lg shadow-sm;
  }

  .alert-info {
    @apply alert-card border-l-blue-500 bg-blue-50 text-blue-900;
  }

  .alert-warning {
    @apply alert-card border-l-yellow-500 bg-yellow-50 text-yellow-900;
  }

  .alert-error {
    @apply alert-card border-l-red-500 bg-red-50 text-red-900;
  }

  .alert-success {
    @apply alert-card border-l-green-500 bg-green-50 text-green-900;
  }

  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }

  .btn-default {
    @apply btn bg-primary text-primary-foreground hover:bg-primary/90 h-10 py-2 px-4;
  }

  .btn-destructive {
    @apply btn bg-destructive text-destructive-foreground hover:bg-destructive/90 h-10 py-2 px-4;
  }

  .btn-outline {
    @apply btn border border-input hover:bg-accent hover:text-accent-foreground h-10 py-2 px-4;
  }

  .btn-secondary {
    @apply btn bg-secondary text-secondary-foreground hover:bg-secondary/80 h-10 py-2 px-4;
  }

  .btn-ghost {
    @apply btn hover:bg-accent hover:text-accent-foreground h-10 py-2 px-4;
  }

  .btn-link {
    @apply btn underline-offset-4 hover:underline text-primary h-10 py-2 px-4;
  }

  .btn-sm {
    @apply h-9 px-3 rounded-md;
  }

  .btn-lg {
    @apply h-11 px-8 rounded-md;
  }

  /* Input styles */
  .input {
    @apply flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  /* Loading states */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary;
  }

  .loading-dots::after {
    content: '';
    animation: loading-dots 1.5s infinite;
  }

  @keyframes loading-dots {
    0%, 20% { content: '.'; }
    40% { content: '..'; }
    60%, 100% { content: '...'; }
  }

  /* Skeleton loading */
  .skeleton {
    @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
  }

  /* Data visualization */
  .chart-container {
    @apply w-full h-64 p-4 bg-white border border-gray-200 rounded-lg shadow-sm;
  }

  .progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700;
  }

  .progress-fill {
    @apply bg-primary h-2.5 rounded-full transition-all duration-300 ease-in-out;
  }

  /* Mobile optimizations */
  @media (max-width: 768px) {
    .mobile-hidden {
      @apply hidden;
    }
    
    .mobile-full {
      @apply w-full;
    }
    
    .mobile-stack {
      @apply flex-col space-y-2 space-x-0;
    }
  }

  /* High contrast mode */
  @media (prefers-contrast: high) {
    .field-card {
      @apply border-2 border-green-600;
    }
    
    .sensor-card {
      @apply border-2 border-gray-600;
    }
  }

  /* Reduced motion */
  @media (prefers-reduced-motion: reduce) {
    .animate-spin,
    .animate-pulse,
    .animate-bounce {
      animation: none;
    }
    
    .transition-all,
    .transition-colors,
    .transition-shadow {
      transition: none;
    }
  }
}

@layer utilities {
  /* Text utilities */
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent;
  }

  /* Layout utilities */
  .center {
    @apply flex items-center justify-center;
  }

  .center-x {
    @apply flex justify-center;
  }

  .center-y {
    @apply flex items-center;
  }

  /* Spacing utilities */
  .space-y-px > * + * {
    margin-top: 1px;
  }

  /* Agriculture-specific utilities */
  .soil-moisture-low {
    @apply text-red-600 bg-red-50;
  }

  .soil-moisture-medium {
    @apply text-yellow-600 bg-yellow-50;
  }

  .soil-moisture-high {
    @apply text-green-600 bg-green-50;
  }

  .crop-health-excellent {
    @apply text-green-700 bg-green-100;
  }

  .crop-health-good {
    @apply text-green-600 bg-green-50;
  }

  .crop-health-fair {
    @apply text-yellow-600 bg-yellow-50;
  }

  .crop-health-poor {
    @apply text-red-600 bg-red-50;
  }

  /* Weather condition utilities */
  .weather-sunny {
    @apply text-yellow-600 bg-yellow-50;
  }

  .weather-cloudy {
    @apply text-gray-600 bg-gray-50;
  }

  .weather-rainy {
    @apply text-blue-600 bg-blue-50;
  }

  .weather-stormy {
    @apply text-purple-600 bg-purple-50;
  }
}
