// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ================================
// USER MANAGEMENT & AUTHENTICATION
// ================================

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  firstName String
  lastName  String
  phone     String?
  avatar    String?
  role      UserRole @default(FARMER)
  status    UserStatus @default(ACTIVE)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  lastLogin DateTime?
  
  // Relations
  farms     Farm[]
  sessions  UserSession[]
  alerts    Alert[]
  
  @@map("users")
}

model UserSession {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("user_sessions")
}

enum UserRole {
  FARMER
  AGRONOMIST
  COOPERATIVE
  ADMIN
  TECHNICIAN
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

// ================================
// FARM & FIELD MANAGEMENT
// ================================

model Farm {
  id          String  @id @default(cuid())
  name        String
  description String?
  address     String
  latitude    Float
  longitude   Float
  totalArea   Float   // in hectares
  timezone    String  @default("Europe/Paris")
  
  // Owner information
  ownerId String
  owner   User   @relation(fields: [ownerId], references: [id])
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  fields        Field[]
  sensors       Sensor[]
  weatherStations WeatherStation[]
  
  @@map("farms")
}

model Field {
  id          String     @id @default(cuid())
  name        String
  description String?
  area        Float      // in hectares
  soilType    SoilType
  slope       Float?     // in degrees
  elevation   Float?     // in meters
  
  // Geographic data (polygon coordinates)
  coordinates Json       // GeoJSON polygon
  
  // Farm relation
  farmId String
  farm   Farm   @relation(fields: [farmId], references: [id])
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  crops         Crop[]
  soilAnalyses  SoilAnalysis[]
  irrigationZones IrrigationZone[]
  
  @@map("fields")
}

enum SoilType {
  CLAY
  SANDY
  LOAMY
  SILTY
  PEATY
  CHALKY
}

// ================================
// CROP MANAGEMENT
// ================================

model Crop {
  id          String     @id @default(cuid())
  name        String
  variety     String
  cropType    CropType
  plantingDate DateTime
  expectedHarvestDate DateTime?
  actualHarvestDate   DateTime?
  status      CropStatus @default(PLANTED)
  
  // Yield data
  expectedYield Float?    // tons per hectare
  actualYield   Float?    // tons per hectare
  
  // Field relation
  fieldId String
  field   Field  @relation(fields: [fieldId], references: [id])
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  treatments    Treatment[]
  healthRecords CropHealthRecord[]
  harvests      Harvest[]
  
  @@map("crops")
}

enum CropType {
  CEREALS
  VEGETABLES
  FRUITS
  LEGUMES
  OILSEEDS
  FORAGE
  INDUSTRIAL
}

enum CropStatus {
  PLANNED
  PLANTED
  GROWING
  FLOWERING
  RIPENING
  READY_TO_HARVEST
  HARVESTED
  FAILED
}

model Treatment {
  id          String        @id @default(cuid())
  type        TreatmentType
  product     String
  dosage      Float
  unit        String
  applicationDate DateTime
  method      String?
  notes       String?
  cost        Float?
  
  // Crop relation
  cropId String
  crop   Crop   @relation(fields: [cropId], references: [id])
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("treatments")
}

enum TreatmentType {
  FERTILIZER
  PESTICIDE
  HERBICIDE
  FUNGICIDE
  INSECTICIDE
  IRRIGATION
  OTHER
}

// ================================
// HEALTH & MONITORING
// ================================

model CropHealthRecord {
  id          String           @id @default(cuid())
  date        DateTime
  healthScore Float            // 0-100
  issues      CropHealthIssue[]
  notes       String?
  images      String[]         // URLs to images
  
  // AI Analysis results
  aiAnalysis  Json?            // AI model predictions
  confidence  Float?           // AI confidence score
  
  // Crop relation
  cropId String
  crop   Crop   @relation(fields: [cropId], references: [id])
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("crop_health_records")
}

enum CropHealthIssue {
  DISEASE_FUNGAL
  DISEASE_BACTERIAL
  DISEASE_VIRAL
  PEST_INSECTS
  PEST_RODENTS
  NUTRIENT_DEFICIENCY
  WATER_STRESS
  HEAT_STRESS
  COLD_DAMAGE
  WEED_PRESSURE
  OTHER
}

// ================================
// SOIL MANAGEMENT
// ================================

model SoilAnalysis {
  id          String   @id @default(cuid())
  date        DateTime
  depth       Float    // in cm
  
  // Chemical properties
  ph          Float
  organicMatter Float  // percentage
  nitrogen    Float    // ppm
  phosphorus  Float    // ppm
  potassium   Float    // ppm
  calcium     Float?   // ppm
  magnesium   Float?   // ppm
  sulfur      Float?   // ppm
  
  // Physical properties
  moisture    Float?   // percentage
  temperature Float?   // celsius
  compaction  Float?   // kg/cm²
  
  // Field relation
  fieldId String
  field   Field  @relation(fields: [fieldId], references: [id])
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("soil_analyses")
}

// ================================
// IOT & SENSOR DATA
// ================================

model Sensor {
  id          String     @id @default(cuid())
  name        String
  type        SensorType
  model       String?
  serialNumber String?   @unique
  latitude    Float
  longitude   Float
  status      SensorStatus @default(ACTIVE)
  
  // Configuration
  measurementInterval Int @default(300) // seconds
  batteryLevel       Float?
  lastMaintenance    DateTime?
  
  // Farm relation
  farmId String
  farm   Farm   @relation(fields: [farmId], references: [id])
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  lastDataReceived DateTime?
  
  // Relations
  readings SensorReading[]
  
  @@map("sensors")
}

enum SensorType {
  SOIL_MOISTURE
  SOIL_TEMPERATURE
  SOIL_PH
  AIR_TEMPERATURE
  AIR_HUMIDITY
  WIND_SPEED
  WIND_DIRECTION
  RAINFALL
  SOLAR_RADIATION
  LEAF_WETNESS
  PRESSURE
  CO2
  MULTI_SENSOR
}

enum SensorStatus {
  ACTIVE
  INACTIVE
  MAINTENANCE
  ERROR
}

model SensorReading {
  id        String   @id @default(cuid())
  timestamp DateTime
  value     Float
  unit      String
  quality   ReadingQuality @default(GOOD)
  
  // Sensor relation
  sensorId String
  sensor   Sensor @relation(fields: [sensorId], references: [id])
  
  // Timestamps
  createdAt DateTime @default(now())
  
  @@map("sensor_readings")
  @@index([sensorId, timestamp])
}

enum ReadingQuality {
  EXCELLENT
  GOOD
  FAIR
  POOR
  ERROR
}

// ================================
// WEATHER DATA
// ================================

model WeatherStation {
  id          String  @id @default(cuid())
  name        String
  latitude    Float
  longitude   Float
  elevation   Float?
  isExternal  Boolean @default(false) // true for external API data
  
  // Farm relation (optional for external stations)
  farmId String?
  farm   Farm?   @relation(fields: [farmId], references: [id])
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  weatherData WeatherData[]
  
  @@map("weather_stations")
}

model WeatherData {
  id            String   @id @default(cuid())
  timestamp     DateTime
  temperature   Float    // celsius
  humidity      Float    // percentage
  pressure      Float?   // hPa
  windSpeed     Float?   // m/s
  windDirection Float?   // degrees
  rainfall      Float?   // mm
  solarRadiation Float?  // W/m²
  uvIndex       Float?
  
  // Weather station relation
  stationId String
  station   WeatherStation @relation(fields: [stationId], references: [id])
  
  // Timestamps
  createdAt DateTime @default(now())
  
  @@map("weather_data")
  @@index([stationId, timestamp])
}

// ================================
// IRRIGATION MANAGEMENT
// ================================

model IrrigationZone {
  id          String  @id @default(cuid())
  name        String
  area        Float   // in hectares
  soilType    SoilType
  cropType    CropType?

  // System configuration
  systemType  IrrigationSystemType
  flowRate    Float?  // liters per minute
  efficiency  Float?  // percentage

  // Field relation
  fieldId String
  field   Field  @relation(fields: [fieldId], references: [id])

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  schedules IrrigationSchedule[]
  events    IrrigationEvent[]

  @@map("irrigation_zones")
}

enum IrrigationSystemType {
  DRIP
  SPRINKLER
  FLOOD
  FURROW
  PIVOT
  MICRO_SPRAY
}

model IrrigationSchedule {
  id          String   @id @default(cuid())
  name        String
  startDate   DateTime
  endDate     DateTime?
  isActive    Boolean  @default(true)

  // Schedule configuration
  frequency   IrrigationFrequency
  duration    Int      // minutes
  startTime   String   // HH:MM format

  // Conditions
  minSoilMoisture Float?
  maxTemperature  Float?
  weatherConditions Json?

  // Zone relation
  zoneId String
  zone   IrrigationZone @relation(fields: [zoneId], references: [id])

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("irrigation_schedules")
}

enum IrrigationFrequency {
  DAILY
  EVERY_OTHER_DAY
  WEEKLY
  BI_WEEKLY
  MONTHLY
  ON_DEMAND
  SENSOR_BASED
}

model IrrigationEvent {
  id          String   @id @default(cuid())
  startTime   DateTime
  endTime     DateTime?
  duration    Int?     // actual duration in minutes
  waterAmount Float?   // liters
  status      IrrigationStatus @default(SCHEDULED)

  // Trigger information
  triggerType IrrigationTrigger
  triggerData Json?

  // Zone relation
  zoneId String
  zone   IrrigationZone @relation(fields: [zoneId], references: [id])

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("irrigation_events")
}

enum IrrigationStatus {
  SCHEDULED
  RUNNING
  COMPLETED
  CANCELLED
  ERROR
}

enum IrrigationTrigger {
  MANUAL
  SCHEDULED
  SENSOR_BASED
  WEATHER_BASED
  AI_RECOMMENDATION
}

// ================================
// HARVEST & YIELD MANAGEMENT
// ================================

model Harvest {
  id          String   @id @default(cuid())
  date        DateTime
  quantity    Float    // tons
  quality     HarvestQuality
  moistureContent Float?
  storageLocation String?
  notes       String?

  // Economic data
  pricePerTon Float?
  totalValue  Float?
  costs       Float?

  // Crop relation
  cropId String
  crop   Crop   @relation(fields: [cropId], references: [id])

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("harvests")
}

enum HarvestQuality {
  EXCELLENT
  GOOD
  AVERAGE
  POOR
  REJECTED
}

// ================================
// AI MODELS & PREDICTIONS
// ================================

model AIModel {
  id          String    @id @default(cuid())
  name        String
  version     String
  type        ModelType
  description String?

  // Model metadata
  accuracy    Float?
  trainingDate DateTime?
  modelPath   String
  parameters  Json?

  // Status
  status      ModelStatus @default(TRAINING)
  isActive    Boolean     @default(false)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  predictions Prediction[]

  @@map("ai_models")
}

enum ModelType {
  DISEASE_DETECTION
  PEST_IDENTIFICATION
  YIELD_PREDICTION
  WEATHER_FORECAST
  IRRIGATION_OPTIMIZATION
  NUTRIENT_RECOMMENDATION
  HARVEST_TIMING
  CROP_CLASSIFICATION
}

enum ModelStatus {
  TRAINING
  READY
  DEPLOYED
  DEPRECATED
  ERROR
}

model Prediction {
  id          String   @id @default(cuid())
  timestamp   DateTime
  inputData   Json
  prediction  Json
  confidence  Float

  // Model relation
  modelId String
  model   AIModel @relation(fields: [modelId], references: [id])

  // Optional relations (depending on prediction type)
  cropId  String?
  fieldId String?

  // Timestamps
  createdAt DateTime @default(now())

  @@map("predictions")
  @@index([modelId, timestamp])
}

// ================================
// ALERTS & NOTIFICATIONS
// ================================

model Alert {
  id          String      @id @default(cuid())
  title       String
  message     String
  type        AlertType
  severity    AlertSeverity
  status      AlertStatus @default(ACTIVE)

  // Alert data
  data        Json?
  threshold   Float?
  actualValue Float?

  // User relation
  userId String
  user   User   @relation(fields: [userId], references: [id])

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  resolvedAt DateTime?

  @@map("alerts")
}

enum AlertType {
  WEATHER_WARNING
  DISEASE_DETECTED
  PEST_ALERT
  IRRIGATION_NEEDED
  HARVEST_READY
  SENSOR_MALFUNCTION
  SYSTEM_ERROR
  MAINTENANCE_DUE
}

enum AlertSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum AlertStatus {
  ACTIVE
  ACKNOWLEDGED
  RESOLVED
  DISMISSED
}

// ================================
// SYSTEM LOGS & AUDIT
// ================================

model SystemLog {
  id        String    @id @default(cuid())
  timestamp DateTime  @default(now())
  level     LogLevel
  message   String
  context   Json?
  userId    String?

  @@map("system_logs")
  @@index([timestamp, level])
}

enum LogLevel {
  DEBUG
  INFO
  WARN
  ERROR
  FATAL
}
