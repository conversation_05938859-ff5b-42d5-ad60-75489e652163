version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: aigricole-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: aigricole_dev
      POSTGRES_USER: aigricole
      POSTGRES_PASSWORD: aigricole_dev_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./packages/database/init:/docker-entrypoint-initdb.d
    networks:
      - aigricole-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U aigricole -d aigricole_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: aigricole-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - aigricole-network
    command: redis-server --appendonly yes --requirepass aigricole_redis_password
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # MQTT Broker for IoT
  mosquitto:
    image: eclipse-mosquitto:2.0
    container_name: aigricole-mqtt
    restart: unless-stopped
    ports:
      - "1883:1883"
      - "9001:9001"
    volumes:
      - ./deployment/mqtt/mosquitto.conf:/mosquitto/config/mosquitto.conf
      - mosquitto_data:/mosquitto/data
      - mosquitto_logs:/mosquitto/log
    networks:
      - aigricole-network

  # MinIO for Object Storage (S3 compatible)
  minio:
    image: minio/minio:latest
    container_name: aigricole-minio
    restart: unless-stopped
    environment:
      MINIO_ROOT_USER: aigricole
      MINIO_ROOT_PASSWORD: aigricole_minio_password
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    networks:
      - aigricole-network
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Elasticsearch for logs and search
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: aigricole-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - aigricole-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Kibana for log visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: aigricole-kibana
    restart: unless-stopped
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    networks:
      - aigricole-network
    depends_on:
      elasticsearch:
        condition: service_healthy

  # Grafana for monitoring
  grafana:
    image: grafana/grafana:latest
    container_name: aigricole-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=aigricole_grafana_password
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./deployment/grafana/provisioning:/etc/grafana/provisioning
    networks:
      - aigricole-network

  # Prometheus for metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: aigricole-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./deployment/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - aigricole-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  mosquitto_data:
    driver: local
  mosquitto_logs:
    driver: local
  minio_data:
    driver: local
  elasticsearch_data:
    driver: local
  grafana_data:
    driver: local
  prometheus_data:
    driver: local

networks:
  aigricole-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
