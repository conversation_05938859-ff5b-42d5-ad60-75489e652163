'use client';

import * as React from 'react';
import { motion } from 'framer-motion';
import { Star, Quote } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

const testimonials = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Maraîchère Bio',
    location: 'Provence-Alpes-Côte d\'Azur',
    avatar: '/avatars/marie-dubois.jpg',
    rating: 5,
    quote: 'AIgricole a révolutionné ma gestion des serres. L\'irrigation automatisée et la détection précoce des maladies m\'ont permis d\'augmenter ma production de 40% tout en réduisant mes pertes.',
    metrics: {
      production: '+40%',
      pertes: '-60%',
      temps: '-3h/jour',
    },
    farmSize: '12 hectares',
    crops: ['Tomates', 'Courgettes', 'Aubergines'],
  },
  {
    id: 2,
    name: '<PERSON>',
    role: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    location: 'Centre-Val de Loire',
    avatar: '/avatars/pierre-martin.jpg',
    rating: 5,
    quote: 'Grâce aux prédictions de rendement et à l\'optimisation des intrants, j\'ai économisé 25% sur mes coûts de production. L\'interface est intuitive même pour quelqu\'un comme moi qui n\'est pas très tech.',
    metrics: {
      economies: '25%',
      rendement: '+18%',
      intrants: '-30%',
    },
    farmSize: '280 hectares',
    crops: ['Blé', 'Maïs', 'Colza'],
  },
  {
    id: 3,
    name: 'Sophie Leroy',
    role: 'Viticultrice',
    location: 'Bourgogne-Franche-Comté',
    avatar: '/avatars/sophie-leroy.jpg',
    rating: 5,
    quote: 'La surveillance par satellite et les alertes météo m\'ont sauvé ma récolte l\'année dernière. J\'ai pu anticiper le mildiou et traiter au bon moment. Un investissement qui se rentabilise dès la première saison.',
    metrics: {
      recolte: '100%',
      traitements: '-50%',
      qualite: '+15%',
    },
    farmSize: '45 hectares',
    crops: ['Pinot Noir', 'Chardonnay'],
  },
  {
    id: 4,
    name: 'Thomas Rousseau',
    role: 'Éleveur Laitier',
    location: 'Bretagne',
    avatar: '/avatars/thomas-rousseau.jpg',
    rating: 5,
    quote: 'Le suivi des pâturages et l\'optimisation de l\'alimentation ont considérablement amélioré la santé de mon troupeau. La production laitière a augmenté et les coûts vétérinaires ont diminué.',
    metrics: {
      production: '+22%',
      sante: '+35%',
      couts: '-20%',
    },
    farmSize: '150 hectares',
    crops: ['Prairies', 'Maïs fourrage'],
  },
  {
    id: 5,
    name: 'Julien Moreau',
    role: 'Arboriculteur',
    location: 'Occitanie',
    avatar: '/avatars/julien-moreau.jpg',
    rating: 5,
    quote: 'L\'analyse par IA de mes vergers m\'aide à détecter les problèmes avant qu\'ils ne deviennent critiques. La gestion de l\'irrigation goutte-à-goutte est parfaitement optimisée.',
    metrics: {
      detection: '95%',
      eau: '-35%',
      qualite: '+25%',
    },
    farmSize: '80 hectares',
    crops: ['Pommiers', 'Pêchers', 'Abricotiers'],
  },
  {
    id: 6,
    name: 'Isabelle Petit',
    role: 'Coopérative Agricole',
    location: 'Hauts-de-France',
    avatar: '/avatars/isabelle-petit.jpg',
    rating: 5,
    quote: 'Nous avons déployé AIgricole pour 150 adhérents. Les résultats sont exceptionnels : amélioration des rendements, réduction des coûts et meilleure traçabilité pour nos clients.',
    metrics: {
      adherents: '150',
      satisfaction: '98%',
      roi: '300%',
    },
    farmSize: '12,000 hectares',
    crops: ['Betteraves', 'Pommes de terre', 'Blé'],
  },
];

export function TestimonialsSection() {
  const [activeTestimonial, setActiveTestimonial] = React.useState(0);

  return (
    <section className="py-24 bg-gradient-to-br from-green-50 to-blue-50">
      <div className="container">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl font-bold tracking-tight sm:text-4xl mb-4"
          >
            Ce que disent nos agriculteurs
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-xl text-muted-foreground max-w-3xl mx-auto"
          >
            Découvrez comment AIgricole transforme le quotidien des agriculteurs partout en France
          </motion.p>
        </div>

        {/* Featured Testimonial */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <Card className="max-w-4xl mx-auto bg-white shadow-xl">
            <CardContent className="p-8 md:p-12">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
                {/* Testimonial Content */}
                <div className="lg:col-span-2">
                  <Quote className="w-12 h-12 text-primary/20 mb-6" />
                  <blockquote className="text-xl md:text-2xl text-gray-700 mb-6 leading-relaxed">
                    {testimonials[activeTestimonial].quote}
                  </blockquote>
                  
                  {/* Rating */}
                  <div className="flex items-center mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className="w-5 h-5 text-yellow-400 fill-current"
                      />
                    ))}
                  </div>

                  {/* Author Info */}
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-gray-300 rounded-full" />
                    <div>
                      <div className="font-semibold text-lg text-gray-900">
                        {testimonials[activeTestimonial].name}
                      </div>
                      <div className="text-primary font-medium">
                        {testimonials[activeTestimonial].role}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {testimonials[activeTestimonial].location} • {testimonials[activeTestimonial].farmSize}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Metrics */}
                <div className="space-y-6">
                  <h4 className="font-semibold text-lg text-gray-900 mb-4">
                    Résultats obtenus
                  </h4>
                  {Object.entries(testimonials[activeTestimonial].metrics).map(([key, value]) => (
                    <div key={key} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                      <span className="text-sm text-gray-600 capitalize">
                        {key === 'production' && 'Production'}
                        {key === 'pertes' && 'Réduction pertes'}
                        {key === 'temps' && 'Gain de temps'}
                        {key === 'economies' && 'Économies'}
                        {key === 'rendement' && 'Rendement'}
                        {key === 'intrants' && 'Réduction intrants'}
                        {key === 'recolte' && 'Récolte sauvée'}
                        {key === 'traitements' && 'Moins de traitements'}
                        {key === 'qualite' && 'Amélioration qualité'}
                        {key === 'sante' && 'Santé troupeau'}
                        {key === 'couts' && 'Réduction coûts'}
                        {key === 'detection' && 'Précision détection'}
                        {key === 'eau' && 'Économie eau'}
                        {key === 'adherents' && 'Adhérents'}
                        {key === 'satisfaction' && 'Satisfaction'}
                        {key === 'roi' && 'ROI'}
                      </span>
                      <span className="font-bold text-primary">{value}</span>
                    </div>
                  ))}
                  
                  {/* Crops */}
                  <div className="pt-4 border-t border-gray-200">
                    <div className="text-sm text-gray-600 mb-2">Cultures :</div>
                    <div className="flex flex-wrap gap-2">
                      {testimonials[activeTestimonial].crops.map((crop) => (
                        <span
                          key={crop}
                          className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full"
                        >
                          {crop}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Testimonial Navigation */}
        <div className="flex justify-center mb-16">
          <div className="flex space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setActiveTestimonial(index)}
                className={`w-3 h-3 rounded-full transition-colors ${
                  index === activeTestimonial
                    ? 'bg-primary'
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>
        </div>

        {/* All Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.slice(0, 6).map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-6">
                  {/* Rating */}
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="w-4 h-4 text-yellow-400 fill-current"
                      />
                    ))}
                  </div>

                  {/* Quote */}
                  <blockquote className="text-gray-700 mb-6 line-clamp-4">
                    "{testimonial.quote}"
                  </blockquote>

                  {/* Author */}
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-300 rounded-full" />
                    <div>
                      <div className="font-semibold text-gray-900">
                        {testimonial.name}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {testimonial.role}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-white rounded-2xl p-8 shadow-lg max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold mb-4">
              Rejoignez plus de 10,000 agriculteurs satisfaits
            </h3>
            <p className="text-muted-foreground mb-6">
              Commencez votre essai gratuit de 30 jours et découvrez comment AIgricole 
              peut transformer votre exploitation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary px-8 py-3">
                Essai gratuit 30 jours
              </button>
              <button className="btn-outline px-8 py-3">
                Demander une démo
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
