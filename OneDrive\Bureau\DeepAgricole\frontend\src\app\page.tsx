'use client';

import { useState, useEffect } from 'react';

export default function HomePage() {
  const [backendStatus, setBackendStatus] = useState('Vérification...');
  const [stats, setStats] = useState(null);

  useEffect(() => {
    // Test de connexion au backend
    fetch('http://localhost:4000/health')
      .then(res => res.json())
      .then(data => {
        setBackendStatus('✅ Connecté');
        // Récupérer les stats du dashboard
        return fetch('http://localhost:4000/api/dashboard/stats');
      })
      .then(res => res.json())
      .then(data => {
        setStats(data.stats);
      })
      .catch(err => {
        setBackendStatus('❌ Déconnecté');
        console.error('Erreur de connexion:', err);
      });
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-600 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-xl">🌱</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">AIgricole</h1>
                <p className="text-sm text-gray-600">Suite d'IA Agricole Complète</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm">
                <span className="text-gray-600">Backend: </span>
                <span className={backendStatus.includes('✅') ? 'text-green-600' : 'text-red-600'}>
                  {backendStatus}
                </span>
              </div>
              <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                Se connecter
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">

        {/* Hero Section */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Révolutionnez votre agriculture avec l'IA
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Découvrez comment l'intelligence artificielle peut transformer votre exploitation
            agricole avec des solutions innovantes pour optimiser vos rendements.
          </p>
        </div>

        {/* Stats Dashboard */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Parcelles</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalFields}</p>
                </div>
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <span className="text-green-600">🏞️</span>
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-2">{stats.totalArea} hectares</p>
            </div>

            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Capteurs actifs</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.activeSensors}</p>
                </div>
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-blue-600">📡</span>
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-2">Collecte temps réel</p>
            </div>

            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Rendement moyen</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.avgYield} t/ha</p>
                </div>
                <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <span className="text-yellow-600">📈</span>
                </div>
              </div>
              <p className="text-xs text-green-600 mt-2">+12% vs année dernière</p>
            </div>

            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">ROI</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.roi}%</p>
                </div>
                <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                  <span className="text-purple-600">💰</span>
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-2">Retour sur investissement</p>
            </div>
          </div>
        )}

        {/* Detailed Features */}
        <Suspense fallback={<LoadingSpinner />}>
          <FeaturesSection />
        </Suspense>

        {/* Statistics */}
        <Suspense fallback={<LoadingSpinner />}>
          <StatsSection />
        </Suspense>

        {/* Solutions by Farm Type */}
        <section id="solutions" className="py-24">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
                Solutions adaptées à votre exploitation
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Que vous soyez céréalier, maraîcher ou éleveur, AIgricole s'adapte à vos besoins spécifiques
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="text-2xl">Grandes Cultures</CardTitle>
                  <CardDescription>
                    Céréales, oléagineux, protéagineux
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Prédiction de rendement par parcelle
                    </li>
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Optimisation des intrants NPK
                    </li>
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Détection précoce des maladies
                    </li>
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Planning optimal des interventions
                    </li>
                  </ul>
                  <Button className="w-full" variant="outline" asChild>
                    <Link href="/solutions/grandes-cultures">
                      En savoir plus
                    </Link>
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow border-primary/20">
                <CardHeader>
                  <Badge className="w-fit mb-2">Populaire</Badge>
                  <CardTitle className="text-2xl">Maraîchage</CardTitle>
                  <CardDescription>
                    Légumes, fruits, cultures spécialisées
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Irrigation de précision automatisée
                    </li>
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Surveillance qualité des fruits
                    </li>
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Gestion des serres connectées
                    </li>
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Traçabilité complète des produits
                    </li>
                  </ul>
                  <Button className="w-full" asChild>
                    <Link href="/auth/register">
                      Commencer l'essai gratuit
                    </Link>
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="text-2xl">Élevage</CardTitle>
                  <CardDescription>
                    Bovins, ovins, volailles
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Surveillance santé du cheptel
                    </li>
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Optimisation de l'alimentation
                    </li>
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Gestion des pâturages
                    </li>
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Prédiction de reproduction
                    </li>
                  </ul>
                  <Button className="w-full" variant="outline" asChild>
                    <Link href="/solutions/elevage">
                      En savoir plus
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Testimonials */}
        <Suspense fallback={<LoadingSpinner />}>
          <TestimonialsSection />
        </Suspense>

        {/* CTA Section */}
        <Suspense fallback={<LoadingSpinner />}>
          <CTASection />
        </Suspense>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
}
