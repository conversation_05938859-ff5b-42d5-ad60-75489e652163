const express = require('express');
const cors = require('cors');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const http = require('http');

// Import IoT modules
const mqttBroker = require('./src/iot/mqttBroker');
const IoTDataHandler = require('./src/iot/dataHandler');

const app = express();
const PORT = process.env.PORT || 4000;
const WS_PORT = process.env.WS_PORT || 4001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Database setup
const dbDir = path.join(__dirname, 'database');
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

const dbPath = path.join(dbDir, 'aigricole_dev.db');
const db = new sqlite3.Database(dbPath);

// Initialize IoT data handler
const iotHandler = new IoTDataHandler(db);

// Store real-time data for polling
const realtimeData = {
  latestReadings: [],
  alerts: []
};

// Store latest sensor data (simplified real-time without WebSocket)
function storeRealtimeData(data) {
  realtimeData.latestReadings.unshift({
    ...data,
    timestamp: new Date().toISOString()
  });

  // Keep only last 50 readings
  if (realtimeData.latestReadings.length > 50) {
    realtimeData.latestReadings = realtimeData.latestReadings.slice(0, 50);
  }
}

// Initialize database
function initializeDatabase() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Create tables (same as before)
      db.run(`CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        first_name TEXT NOT NULL,
        last_name TEXT NOT NULL,
        role TEXT DEFAULT 'farmer',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`);

      db.run(`CREATE TABLE IF NOT EXISTS farms (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        total_area REAL,
        farm_type TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      )`);

      db.run(`CREATE TABLE IF NOT EXISTS fields (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        farm_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        area REAL NOT NULL,
        soil_type TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (farm_id) REFERENCES farms(id)
      )`);

      db.run(`CREATE TABLE IF NOT EXISTS sensors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        field_id INTEGER NOT NULL,
        sensor_type TEXT NOT NULL,
        brand TEXT,
        model TEXT,
        serial_number TEXT UNIQUE,
        battery_level INTEGER,
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (field_id) REFERENCES fields(id)
      )`);

      db.run(`CREATE TABLE IF NOT EXISTS iot_readings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sensor_id INTEGER NOT NULL,
        reading_type TEXT NOT NULL,
        value REAL NOT NULL,
        unit TEXT NOT NULL,
        quality_score REAL DEFAULT 1.0,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (sensor_id) REFERENCES sensors(id)
      )`, (err) => {
        if (err) {
          console.error('Database initialization error:', err);
          reject(err);
        } else {
          console.log('✅ Database initialized successfully');
          resolve();
        }
      });
    });
  });
}

// Seed database with test data
function seedDatabase() {
  return new Promise((resolve, reject) => {
    // Check if data already exists
    db.get("SELECT COUNT(*) as count FROM users", (err, row) => {
      if (err) {
        reject(err);
        return;
      }
      
      if (row.count > 0) {
        console.log('✅ Database already seeded');
        resolve();
        return;
      }

      // Insert test data
      db.serialize(() => {
        db.run(`INSERT INTO users (email, password_hash, first_name, last_name) 
                VALUES ('<EMAIL>', 'hashed_password', 'Jean', 'Dupont')`);

        db.run(`INSERT INTO farms (user_id, name, description, total_area, farm_type) 
                VALUES (1, 'Ferme des Trois Chênes', 'Exploitation familiale', 150.5, 'mixed')`);

        db.run(`INSERT INTO fields (farm_id, name, area, soil_type) 
                VALUES (1, 'Parcelle Nord', 45.2, 'limon'),
                       (1, 'Parcelle Sud', 25.8, 'argilo-limoneux')`);

        db.run(`INSERT INTO sensors (field_id, sensor_type, brand, model, serial_number, battery_level) 
                VALUES (1, 'soil_moisture', 'AgroSense', 'SM-2000', 'AS-SM-001', 85),
                       (1, 'temperature', 'AgroSense', 'TEMP-150', 'AS-TEMP-001', 92),
                       (2, 'ph', 'SoilTech', 'PH-Pro', 'ST-PH-001', 78),
                       (1, 'humidity', 'AgroSense', 'HUM-200', 'AS-HUM-001', 88),
                       (1, 'light_intensity', 'AgroSense', 'LIGHT-300', 'AS-LIGHT-001', 95)`, (err) => {
          if (err) {
            reject(err);
          } else {
            console.log('✅ Database seeded with test data');
            resolve();
          }
        });
      });
    });
  });
}

// API Routes (same as before, plus new IoT endpoints)

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    database: 'connected',
    mqtt: mqttBroker.isConnectedToBroker(),
    websocket: `ws://localhost:${WS_PORT}`,
    version: '1.1.0'
  });
});

// Dashboard stats with real-time IoT data
app.get('/api/dashboard/stats', (req, res) => {
  const queries = {
    farms: "SELECT COUNT(*) as count, SUM(total_area) as total_area FROM farms",
    fields: "SELECT COUNT(*) as count FROM fields",
    sensors: "SELECT COUNT(*) as count FROM sensors WHERE status = 'active'",
    recentReadings: "SELECT COUNT(*) as count FROM iot_readings WHERE datetime(timestamp) > datetime('now', '-1 hour')"
  };

  Promise.all([
    new Promise((resolve, reject) => {
      db.get(queries.farms, (err, row) => err ? reject(err) : resolve(row));
    }),
    new Promise((resolve, reject) => {
      db.get(queries.fields, (err, row) => err ? reject(err) : resolve(row));
    }),
    new Promise((resolve, reject) => {
      db.get(queries.sensors, (err, row) => err ? reject(err) : resolve(row));
    }),
    new Promise((resolve, reject) => {
      db.get(queries.recentReadings, (err, row) => err ? reject(err) : resolve(row));
    })
  ]).then(([farms, fields, sensors, readings]) => {
    res.json({
      stats: {
        totalFields: fields.count,
        totalArea: Math.round((farms.total_area || 0) * 10) / 10,
        activeSensors: sensors.count,
        alerts: 2,
        avgYield: 8.2,
        waterSaved: 15,
        roi: 18
      },
      alerts: [
        {
          id: 1,
          type: 'IRRIGATION_NEEDED',
          severity: 'MEDIUM',
          title: 'Irrigation recommandée',
          message: 'La parcelle Nord nécessite un arrosage',
          createdAt: new Date().toISOString()
        },
        {
          id: 2,
          type: 'SENSOR_BATTERY_LOW',
          severity: 'LOW',
          title: 'Batterie faible',
          message: 'Le capteur ST-PH-001 a une batterie faible (78%)',
          createdAt: new Date().toISOString()
        }
      ],
      weather: {
        temperature: 22,
        humidity: 65,
        windSpeed: 12,
        condition: 'sunny'
      },
      cropHealth: {
        overall: 85,
        diseases: 0,
        pests: 1,
        nutrition: 90
      },
      recentActivity: readings.count,
      iot: {
        connected: mqttBroker.isConnectedToBroker(),
        activeTopics: mqttBroker.getActiveTopics().length,
        realtimeReadings: realtimeData.latestReadings.length
      }
    });
  }).catch(err => {
    console.error('Dashboard stats error:', err);
    res.status(500).json({ error: 'Failed to fetch dashboard stats' });
  });
});

// IoT endpoint to receive sensor data
app.post('/api/iot/data', async (req, res) => {
  try {
    const result = await iotHandler.processIoTData(req.body);
    
    if (result.success) {
      // Store for real-time polling
      storeRealtimeData({
        type: 'sensor_data',
        data: req.body,
        qualityScore: result.qualityScore
      });

      res.json({ success: true, id: result.id, qualityScore: result.qualityScore });
    } else {
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('IoT data endpoint error:', error);
    res.status(500).json({ error: 'Failed to process IoT data' });
  }
});

// Get sensor statistics
app.get('/api/sensors/:id/stats', async (req, res) => {
  try {
    const sensorId = req.params.id;
    const hours = req.query.hours || 24;
    
    const stats = await iotHandler.getSensorStatistics(sensorId, hours);
    res.json({ stats });
  } catch (error) {
    console.error('Sensor stats error:', error);
    res.status(500).json({ error: 'Failed to fetch sensor statistics' });
  }
});

// Get all sensors with latest readings
app.get('/api/sensors', (req, res) => {
  db.all(`SELECT s.*, f.name as field_name, fa.name as farm_name,
          (SELECT value FROM iot_readings WHERE sensor_id = s.id ORDER BY timestamp DESC LIMIT 1) as latest_value,
          (SELECT timestamp FROM iot_readings WHERE sensor_id = s.id ORDER BY timestamp DESC LIMIT 1) as latest_reading
          FROM sensors s 
          JOIN fields f ON s.field_id = f.id 
          JOIN farms fa ON f.farm_id = fa.id`, (err, rows) => {
    if (err) {
      console.error('Sensors query error:', err);
      res.status(500).json({ error: 'Failed to fetch sensors' });
    } else {
      res.json({ sensors: rows });
    }
  });
});

// Start server
async function startServer() {
  try {
    await initializeDatabase();
    await seedDatabase();
    
    // Connect to MQTT broker and start simulation
    await mqttBroker.connect();
    
    // Subscribe to sensor topics
    mqttBroker.subscribe('sensors/+', async (topic, message) => {
      try {
        const result = await iotHandler.processIoTData(message);
        if (result.success) {
          // Broadcast to WebSocket clients
          broadcastToClients({
            type: 'sensor_data',
            topic: topic,
            data: JSON.parse(message),
            qualityScore: result.qualityScore,
            timestamp: new Date().toISOString()
          });
        }
      } catch (error) {
        console.error('Error processing MQTT message:', error);
      }
    });
    
    // Start sensor simulation
    mqttBroker.startSensorSimulation();
    
    app.listen(PORT, () => {
      console.log(`
🌱 AIgricole Backend Server with IoT Started!

📊 Server Information:
   • HTTP Port: ${PORT}
   • WebSocket Port: ${WS_PORT}
   • URL: http://localhost:${PORT}
   • WebSocket: ws://localhost:${WS_PORT}
   • Environment: ${process.env.NODE_ENV || 'development'}
   • Database: SQLite (${dbPath})

🔗 Endpoints:
   • Health Check: http://localhost:${PORT}/health
   • Dashboard Stats: http://localhost:${PORT}/api/dashboard/stats
   • Sensors: http://localhost:${PORT}/api/sensors
   • IoT Data: POST http://localhost:${PORT}/api/iot/data
   • WebSocket: ws://localhost:${WS_PORT}

📡 IoT Features:
   • MQTT Broker: ${mqttBroker.isConnectedToBroker() ? 'Connected' : 'Disconnected'}
   • Real-time Data: Enabled
   • Sensor Simulation: Active
   • Data Validation: Enabled

🚀 Ready to serve agricultural IoT requests!
      `);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

startServer();

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  mqttBroker.disconnect();
  wss.close();
  db.close((err) => {
    if (err) {
      console.error('Error closing database:', err);
    } else {
      console.log('✅ Database connection closed');
    }
    process.exit(0);
  });
});

module.exports = app;
