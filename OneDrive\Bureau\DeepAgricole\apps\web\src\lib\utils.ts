import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Utility function to merge Tailwind CSS classes
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Format a number with locale-specific formatting
 */
export function formatNumber(
  value: number,
  options?: Intl.NumberFormatOptions
): string {
  return new Intl.NumberFormat('fr-FR', options).format(value);
}

/**
 * Format currency values
 */
export function formatCurrency(value: number, currency = 'EUR'): string {
  return formatNumber(value, {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
  });
}

/**
 * Format percentage values
 */
export function formatPercentage(value: number, decimals = 1): string {
  return formatNumber(value, {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
}

/**
 * Format dates with locale-specific formatting
 */
export function formatDate(
  date: Date | string,
  options?: Intl.DateTimeFormatOptions
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    ...options,
  }).format(dateObj);
}

/**
 * Format relative time (e.g., "il y a 2 heures")
 */
export function formatRelativeTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

  const rtf = new Intl.RelativeTimeFormat('fr-FR', { numeric: 'auto' });

  if (diffInSeconds < 60) {
    return rtf.format(-diffInSeconds, 'second');
  } else if (diffInSeconds < 3600) {
    return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
  } else if (diffInSeconds < 86400) {
    return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
  } else if (diffInSeconds < 2592000) {
    return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
  } else if (diffInSeconds < 31536000) {
    return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month');
  } else {
    return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year');
  }
}

/**
 * Truncate text to a specified length
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength).trim() + '...';
}

/**
 * Generate a random ID
 */
export function generateId(prefix = ''): string {
  const id = Math.random().toString(36).substr(2, 9);
  return prefix ? `${prefix}-${id}` : id;
}

/**
 * Debounce function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * Deep clone an object
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T;
  if (typeof obj === 'object') {
    const clonedObj = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
  return obj;
}

/**
 * Check if a value is empty (null, undefined, empty string, empty array, empty object)
 */
export function isEmpty(value: any): boolean {
  if (value === null || value === undefined) return true;
  if (typeof value === 'string') return value.trim().length === 0;
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
}

/**
 * Convert bytes to human readable format
 */
export function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Agriculture-specific utilities
 */

/**
 * Convert hectares to other area units
 */
export function convertArea(hectares: number, unit: 'acres' | 'm2' | 'km2'): number {
  switch (unit) {
    case 'acres':
      return hectares * 2.47105;
    case 'm2':
      return hectares * 10000;
    case 'km2':
      return hectares / 100;
    default:
      return hectares;
  }
}

/**
 * Format area with appropriate unit
 */
export function formatArea(hectares: number, unit: 'ha' | 'acres' | 'm2' | 'km2' = 'ha'): string {
  if (unit === 'ha') {
    return `${formatNumber(hectares, { maximumFractionDigits: 2 })} ha`;
  }
  
  const converted = convertArea(hectares, unit as any);
  const unitLabels = {
    acres: 'acres',
    m2: 'm²',
    km2: 'km²',
  };
  
  return `${formatNumber(converted, { maximumFractionDigits: 2 })} ${unitLabels[unit as keyof typeof unitLabels]}`;
}

/**
 * Convert temperature between Celsius and Fahrenheit
 */
export function convertTemperature(temp: number, from: 'C' | 'F', to: 'C' | 'F'): number {
  if (from === to) return temp;
  
  if (from === 'C' && to === 'F') {
    return (temp * 9/5) + 32;
  } else {
    return (temp - 32) * 5/9;
  }
}

/**
 * Format temperature with unit
 */
export function formatTemperature(temp: number, unit: 'C' | 'F' = 'C'): string {
  return `${formatNumber(temp, { maximumFractionDigits: 1 })}°${unit}`;
}

/**
 * Calculate crop health score color
 */
export function getCropHealthColor(score: number): string {
  if (score >= 80) return 'text-green-600 bg-green-50';
  if (score >= 60) return 'text-yellow-600 bg-yellow-50';
  if (score >= 40) return 'text-orange-600 bg-orange-50';
  return 'text-red-600 bg-red-50';
}

/**
 * Get soil moisture level description
 */
export function getSoilMoistureLevel(percentage: number): {
  level: string;
  color: string;
  description: string;
} {
  if (percentage >= 70) {
    return {
      level: 'Élevé',
      color: 'text-blue-600 bg-blue-50',
      description: 'Sol bien hydraté',
    };
  } else if (percentage >= 40) {
    return {
      level: 'Optimal',
      color: 'text-green-600 bg-green-50',
      description: 'Niveau d\'humidité idéal',
    };
  } else if (percentage >= 20) {
    return {
      level: 'Faible',
      color: 'text-yellow-600 bg-yellow-50',
      description: 'Irrigation recommandée',
    };
  } else {
    return {
      level: 'Critique',
      color: 'text-red-600 bg-red-50',
      description: 'Irrigation urgente nécessaire',
    };
  }
}

/**
 * Calculate days between two dates
 */
export function daysBetween(date1: Date, date2: Date): number {
  const oneDay = 24 * 60 * 60 * 1000;
  return Math.round(Math.abs((date1.getTime() - date2.getTime()) / oneDay));
}

/**
 * Get growing season progress
 */
export function getGrowingSeasonProgress(plantingDate: Date, harvestDate: Date): number {
  const now = new Date();
  const totalDays = daysBetween(plantingDate, harvestDate);
  const elapsedDays = daysBetween(plantingDate, now);
  
  return Math.min(100, Math.max(0, (elapsedDays / totalDays) * 100));
}

/**
 * Format yield with appropriate unit
 */
export function formatYield(yield: number, unit: 't/ha' | 'kg/ha' | 'q/ha' = 't/ha'): string {
  let value = yield;
  
  if (unit === 'kg/ha') {
    value = yield * 1000;
  } else if (unit === 'q/ha') {
    value = yield * 10;
  }
  
  return `${formatNumber(value, { maximumFractionDigits: 2 })} ${unit}`;
}

/**
 * Get weather condition icon and color
 */
export function getWeatherCondition(condition: string): {
  icon: string;
  color: string;
  description: string;
} {
  const conditions: Record<string, any> = {
    sunny: {
      icon: '☀️',
      color: 'text-yellow-600 bg-yellow-50',
      description: 'Ensoleillé',
    },
    cloudy: {
      icon: '☁️',
      color: 'text-gray-600 bg-gray-50',
      description: 'Nuageux',
    },
    rainy: {
      icon: '🌧️',
      color: 'text-blue-600 bg-blue-50',
      description: 'Pluvieux',
    },
    stormy: {
      icon: '⛈️',
      color: 'text-purple-600 bg-purple-50',
      description: 'Orageux',
    },
  };
  
  return conditions[condition.toLowerCase()] || conditions.sunny;
}

/**
 * Validate email address
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate phone number (French format)
 */
export function isValidPhoneNumber(phone: string): boolean {
  const phoneRegex = /^(?:(?:\+|00)33|0)\s*[1-9](?:[\s.-]*\d{2}){4}$/;
  return phoneRegex.test(phone);
}

/**
 * Sleep function for async operations
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}
