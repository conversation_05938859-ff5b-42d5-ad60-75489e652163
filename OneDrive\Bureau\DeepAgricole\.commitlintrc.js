module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    // Type enum - agriculture-specific types
    'type-enum': [
      2,
      'always',
      [
        'feat',      // New feature
        'fix',       // Bug fix
        'docs',      // Documentation
        'style',     // Code style changes
        'refactor',  // Code refactoring
        'perf',      // Performance improvements
        'test',      // Tests
        'build',     // Build system changes
        'ci',        // CI/CD changes
        'chore',     // Maintenance tasks
        'revert',    // Revert changes
        // Agriculture-specific types
        'ai',        // AI model changes
        'iot',       // IoT sensor changes
        'data',      // Data processing changes
        'crop',      // Crop management features
        'weather',   // Weather-related features
        'irrigation', // Irrigation system changes
        'analytics', // Analytics and reporting
        'mobile',    // Mobile app changes
        'offline',   // Offline functionality
      ],
    ],

    // Subject case - sentence case
    'subject-case': [2, 'never', ['start-case', 'pascal-case', 'upper-case']],

    // Subject length
    'subject-max-length': [2, 'always', 72],
    'subject-min-length': [2, 'always', 10],

    // Subject format
    'subject-empty': [2, 'never'],
    'subject-full-stop': [2, 'never', '.'],

    // Body format
    'body-leading-blank': [2, 'always'],
    'body-max-line-length': [2, 'always', 100],

    // Footer format
    'footer-leading-blank': [2, 'always'],
    'footer-max-line-length': [2, 'always', 100],

    // Header format
    'header-max-length': [2, 'always', 100],

    // Scope enum - agriculture-specific scopes
    'scope-enum': [
      2,
      'always',
      [
        // General scopes
        'api',
        'ui',
        'db',
        'auth',
        'config',
        'deps',
        'security',
        'performance',
        'accessibility',
        'i18n',
        'docs',
        'tests',
        'ci',
        'build',
        'deploy',
        
        // Agriculture-specific scopes
        'crops',
        'fields',
        'farms',
        'sensors',
        'weather',
        'irrigation',
        'soil',
        'diseases',
        'pests',
        'fertilizers',
        'harvest',
        'yield',
        'analytics',
        'predictions',
        'alerts',
        'reports',
        'dashboard',
        'mobile',
        'offline',
        'sync',
        'ai-models',
        'computer-vision',
        'satellite',
        'iot',
        'edge',
        'mqtt',
        'storage',
        'cache',
        'search',
        'monitoring',
        'logging',
      ],
    ],

    // Scope case
    'scope-case': [2, 'always', 'kebab-case'],
  },

  // Custom prompt for better commit messages
  prompt: {
    questions: {
      type: {
        description: "Select the type of change that you're committing:",
        enum: {
          feat: {
            description: 'A new feature',
            title: 'Features',
            emoji: '✨',
          },
          fix: {
            description: 'A bug fix',
            title: 'Bug Fixes',
            emoji: '🐛',
          },
          docs: {
            description: 'Documentation only changes',
            title: 'Documentation',
            emoji: '📚',
          },
          style: {
            description: 'Changes that do not affect the meaning of the code',
            title: 'Styles',
            emoji: '💎',
          },
          refactor: {
            description: 'A code change that neither fixes a bug nor adds a feature',
            title: 'Code Refactoring',
            emoji: '📦',
          },
          perf: {
            description: 'A code change that improves performance',
            title: 'Performance Improvements',
            emoji: '🚀',
          },
          test: {
            description: 'Adding missing tests or correcting existing tests',
            title: 'Tests',
            emoji: '🚨',
          },
          build: {
            description: 'Changes that affect the build system or external dependencies',
            title: 'Builds',
            emoji: '🛠',
          },
          ci: {
            description: 'Changes to our CI configuration files and scripts',
            title: 'Continuous Integrations',
            emoji: '⚙️',
          },
          chore: {
            description: "Other changes that don't modify src or test files",
            title: 'Chores',
            emoji: '♻️',
          },
          revert: {
            description: 'Reverts a previous commit',
            title: 'Reverts',
            emoji: '🗑',
          },
          // Agriculture-specific types
          ai: {
            description: 'AI model or machine learning changes',
            title: 'AI/ML',
            emoji: '🤖',
          },
          iot: {
            description: 'IoT sensor or device changes',
            title: 'IoT',
            emoji: '📡',
          },
          data: {
            description: 'Data processing or pipeline changes',
            title: 'Data',
            emoji: '📊',
          },
          crop: {
            description: 'Crop management features',
            title: 'Crops',
            emoji: '🌾',
          },
          weather: {
            description: 'Weather-related features',
            title: 'Weather',
            emoji: '🌤',
          },
          irrigation: {
            description: 'Irrigation system changes',
            title: 'Irrigation',
            emoji: '💧',
          },
          analytics: {
            description: 'Analytics and reporting features',
            title: 'Analytics',
            emoji: '📈',
          },
          mobile: {
            description: 'Mobile app specific changes',
            title: 'Mobile',
            emoji: '📱',
          },
          offline: {
            description: 'Offline functionality changes',
            title: 'Offline',
            emoji: '📴',
          },
        },
      },
      scope: {
        description: 'What is the scope of this change (e.g. component or file name)',
      },
      subject: {
        description: 'Write a short, imperative tense description of the change',
      },
      body: {
        description: 'Provide a longer description of the change',
      },
      isBreaking: {
        description: 'Are there any breaking changes?',
      },
      breakingBody: {
        description: 'A BREAKING CHANGE commit requires a body. Please enter a longer description of the commit itself',
      },
      breaking: {
        description: 'Describe the breaking changes',
      },
      isIssueAffected: {
        description: 'Does this change affect any open issues?',
      },
      issuesBody: {
        description: 'If issues are closed, the commit requires a body. Please enter a longer description of the commit itself',
      },
      issues: {
        description: 'Add issue references (e.g. "fix #123", "re #123".)',
      },
    },
  },
};
