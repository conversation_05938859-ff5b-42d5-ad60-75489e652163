# Database Configuration
DATABASE_URL="postgresql://aigricole:aigricole_dev_password@localhost:5432/aigricole_dev?schema=public"

# Test Database
TEST_DATABASE_URL="postgresql://aigricole:aigricole_dev_password@localhost:5432/aigricole_test?schema=public"

# Redis Configuration
REDIS_URL="redis://:aigricole_redis_password@localhost:6379"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_EXPIRES_IN="7d"

# Encryption
ENCRYPTION_KEY="your-32-character-encryption-key-here"

# External APIs
OPENWEATHER_API_KEY="your-openweather-api-key"
SENTINEL_HUB_CLIENT_ID="your-sentinel-hub-client-id"
SENTINEL_HUB_CLIENT_SECRET="your-sentinel-hub-client-secret"

# File Storage
STORAGE_PROVIDER="local" # local, s3, gcs
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="eu-west-1"
AWS_BUCKET="aigricole-storage"

# MQTT Configuration
MQTT_BROKER_URL="mqtt://localhost:1883"
MQTT_USERNAME="aigricole"
MQTT_PASSWORD="aigricole_mqtt_password"

# Monitoring
SENTRY_DSN="your-sentry-dsn"
LOG_LEVEL="info"

# Development
NODE_ENV="development"
PORT="3000"
