#!/usr/bin/env node

const { spawn, execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset', prefix = '') {
  const timestamp = new Date().toLocaleTimeString();
  console.log(`${colors[color]}[${timestamp}]${prefix ? ` [${prefix}]` : ''} ${message}${colors.reset}`);
}

function checkPrerequisites() {
  log('🔍 Checking prerequisites...', 'blue');
  
  // Check if .env files exist
  const envFiles = [
    'packages/database/.env',
    'apps/web/.env.local',
  ];
  
  const missingEnvFiles = envFiles.filter(file => !fs.existsSync(file));
  
  if (missingEnvFiles.length > 0) {
    log('❌ Missing environment files:', 'red');
    missingEnvFiles.forEach(file => log(`   - ${file}`, 'red'));
    log('Run "npm run setup" first to create environment files.', 'yellow');
    process.exit(1);
  }
  
  // Check if node_modules exist
  if (!fs.existsSync('node_modules')) {
    log('❌ Dependencies not installed. Run "npm install" first.', 'red');
    process.exit(1);
  }
  
  // Check if Prisma client is generated
  if (!fs.existsSync('packages/database/src/generated')) {
    log('⚠️  Prisma client not generated. Generating now...', 'yellow');
    try {
      execSync('npm run db:generate', { stdio: 'inherit' });
    } catch (error) {
      log('❌ Failed to generate Prisma client', 'red');
      process.exit(1);
    }
  }
  
  log('✅ Prerequisites check passed!', 'green');
}

function startService(name, command, color = 'cyan', cwd = process.cwd()) {
  log(`🚀 Starting ${name}...`, color);
  
  const child = spawn('npm', ['run', command], {
    cwd,
    stdio: 'pipe',
    shell: true,
  });
  
  child.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(line => line.trim());
    lines.forEach(line => log(line, color, name));
  });
  
  child.stderr.on('data', (data) => {
    const lines = data.toString().split('\n').filter(line => line.trim());
    lines.forEach(line => log(line, 'red', name));
  });
  
  child.on('close', (code) => {
    if (code !== 0) {
      log(`❌ ${name} exited with code ${code}`, 'red');
    } else {
      log(`✅ ${name} exited successfully`, 'green');
    }
  });
  
  return child;
}

function startDatabaseServices() {
  log('🗄️  Checking database services...', 'blue');
  
  try {
    // Check if Docker is available
    execSync('docker --version', { stdio: 'ignore' });
    
    // Check if services are already running
    try {
      execSync('docker-compose ps postgres redis', { stdio: 'ignore' });
      log('✅ Database services are already running', 'green');
    } catch (error) {
      log('🐳 Starting database services with Docker...', 'blue');
      execSync('docker-compose up -d postgres redis', { stdio: 'inherit' });
      log('✅ Database services started', 'green');
    }
  } catch (error) {
    log('⚠️  Docker not available. Make sure PostgreSQL and Redis are running manually.', 'yellow');
  }
}

function displayBanner() {
  console.log(`
${colors.green}
  ╔═══════════════════════════════════════════════════════════════╗
  ║                                                               ║
  ║                    🌱 AIgricole Development                   ║
  ║                                                               ║
  ║               Revolutionizing Agriculture with AI             ║
  ║                                                               ║
  ╚═══════════════════════════════════════════════════════════════╝
${colors.reset}
  `);
}

function displayUrls() {
  console.log(`
${colors.cyan}📱 Application URLs:${colors.reset}
  
  🌐 Web Dashboard:     http://localhost:3000
  📊 Database Studio:   http://localhost:5555
  📡 API Docs:          http://localhost:3001/docs
  🔍 GraphQL Playground: http://localhost:3001/graphql
  
${colors.yellow}🛠️  Development Tools:${colors.reset}
  
  📈 Bundle Analyzer:   npm run analyze
  🧪 Test Coverage:     npm run test:coverage
  📝 Storybook:         npm run storybook
  🔧 Database Studio:   npm run db:studio
  
${colors.magenta}📚 Documentation:${colors.reset}
  
  📖 User Guide:        docs/user-guide.md
  🏗️  Architecture:      docs/architecture.md
  🔌 API Reference:     docs/api-reference.md
  
  `);
}

async function main() {
  displayBanner();
  
  // Check prerequisites
  checkPrerequisites();
  
  // Start database services
  startDatabaseServices();
  
  // Wait a bit for services to start
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  log('🚀 Starting development environment...', 'green');
  
  // Start all services
  const services = [];
  
  // Web application
  services.push(startService('WEB', 'dev', 'cyan', 'apps/web'));
  
  // API server (if exists)
  if (fs.existsSync('apps/api')) {
    services.push(startService('API', 'dev', 'blue', 'apps/api'));
  }
  
  // Mobile app (if exists and requested)
  if (process.argv.includes('--mobile') && fs.existsSync('apps/mobile')) {
    services.push(startService('MOBILE', 'dev', 'magenta', 'apps/mobile'));
  }
  
  // Edge computing service (if exists and requested)
  if (process.argv.includes('--edge') && fs.existsSync('apps/edge')) {
    services.push(startService('EDGE', 'dev', 'yellow', 'apps/edge'));
  }
  
  // Database studio (if requested)
  if (process.argv.includes('--db-studio')) {
    services.push(startService('DB-STUDIO', 'db:studio', 'green'));
  }
  
  // Storybook (if requested)
  if (process.argv.includes('--storybook')) {
    services.push(startService('STORYBOOK', 'storybook', 'purple'));
  }
  
  // Display URLs after a delay
  setTimeout(() => {
    displayUrls();
  }, 5000);
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    log('\n🛑 Shutting down development environment...', 'yellow');
    
    services.forEach(service => {
      if (service && !service.killed) {
        service.kill('SIGTERM');
      }
    });
    
    setTimeout(() => {
      log('👋 Development environment stopped', 'green');
      process.exit(0);
    }, 2000);
  });
  
  // Keep the process alive
  process.stdin.resume();
}

// Handle errors
process.on('unhandledRejection', (error) => {
  log(`❌ Unhandled error: ${error.message}`, 'red');
  process.exit(1);
});

// Show help
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
${colors.green}AIgricole Development Script${colors.reset}

Usage: npm run dev [options]

Options:
  --mobile      Start mobile app development server
  --edge        Start edge computing service
  --db-studio   Start Prisma Studio for database management
  --storybook   Start Storybook for component development
  --help, -h    Show this help message

Examples:
  npm run dev                    # Start web app only
  npm run dev -- --mobile       # Start web app and mobile app
  npm run dev -- --db-studio    # Start web app and database studio
  npm run dev -- --mobile --edge --storybook  # Start all services

Environment:
  Make sure you have run 'npm run setup' first to configure your environment.
  
Services will be available at:
  - Web App: http://localhost:3000
  - API: http://localhost:3001
  - Mobile: Expo DevTools will open automatically
  - Database Studio: http://localhost:5555
  - Storybook: http://localhost:6006
  `);
  process.exit(0);
}

// Run the development environment
main().catch((error) => {
  log(`❌ Failed to start development environment: ${error.message}`, 'red');
  process.exit(1);
});
