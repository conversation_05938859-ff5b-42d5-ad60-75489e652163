import { PrismaClient } from './generated';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

export async function seedDatabase() {
  console.log('🌱 Starting database seeding...');

  try {
    // Clean existing data
    await cleanDatabase();

    // Create users
    const users = await createUsers();
    console.log(`✅ Created ${users.length} users`);

    // Create farms
    const farms = await createFarms(users);
    console.log(`✅ Created ${farms.length} farms`);

    // Create fields
    const fields = await createFields(farms);
    console.log(`✅ Created ${fields.length} fields`);

    // Create crops
    const crops = await createCrops(fields);
    console.log(`✅ Created ${crops.length} crops`);

    // Create sensors
    const sensors = await createSensors(farms);
    console.log(`✅ Created ${sensors.length} sensors`);

    // Create sensor readings
    await createSensorReadings(sensors);
    console.log(`✅ Created sensor readings`);

    // Create weather stations
    const weatherStations = await createWeatherStations(farms);
    console.log(`✅ Created ${weatherStations.length} weather stations`);

    // Create weather data
    await createWeatherData(weatherStations);
    console.log(`✅ Created weather data`);

    // Create soil analyses
    await createSoilAnalyses(fields);
    console.log(`✅ Created soil analyses`);

    // Create treatments
    await createTreatments(crops);
    console.log(`✅ Created treatments`);

    // Create irrigation zones
    const irrigationZones = await createIrrigationZones(fields);
    console.log(`✅ Created ${irrigationZones.length} irrigation zones`);

    // Create irrigation schedules
    await createIrrigationSchedules(irrigationZones);
    console.log(`✅ Created irrigation schedules`);

    // Create harvests
    await createHarvests(crops);
    console.log(`✅ Created harvests`);

    // Create alerts
    await createAlerts(users);
    console.log(`✅ Created alerts`);

    console.log('🎉 Database seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function cleanDatabase() {
  console.log('🧹 Cleaning existing data...');
  
  // Delete in reverse order of dependencies
  await prisma.alert.deleteMany();
  await prisma.harvest.deleteMany();
  await prisma.irrigationEvent.deleteMany();
  await prisma.irrigationSchedule.deleteMany();
  await prisma.irrigationZone.deleteMany();
  await prisma.treatment.deleteMany();
  await prisma.soilAnalysis.deleteMany();
  await prisma.cropHealthRecord.deleteMany();
  await prisma.weatherData.deleteMany();
  await prisma.weatherStation.deleteMany();
  await prisma.sensorReading.deleteMany();
  await prisma.sensor.deleteMany();
  await prisma.crop.deleteMany();
  await prisma.field.deleteMany();
  await prisma.farm.deleteMany();
  await prisma.userSession.deleteMany();
  await prisma.user.deleteMany();
}

async function createUsers() {
  const hashedPassword = await bcrypt.hash('password123', 10);
  
  return prisma.$transaction([
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Jean',
        lastName: 'Dupont',
        phone: '+33123456789',
        role: 'FARMER',
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Marie',
        lastName: 'Martin',
        phone: '+33987654321',
        role: 'FARMER',
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Pierre',
        lastName: 'Bernard',
        phone: '+33456789123',
        role: 'AGRONOMIST',
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Admin',
        lastName: 'AIgricole',
        role: 'ADMIN',
      },
    }),
  ]);
}

async function createFarms(users: any[]) {
  return prisma.$transaction([
    prisma.farm.create({
      data: {
        name: 'Ferme des Champs Verts',
        description: 'Exploitation céréalière familiale de 150 hectares',
        address: '123 Route des Champs, 45000 Orléans, France',
        latitude: 47.9029,
        longitude: 1.9039,
        totalArea: 150.5,
        ownerId: users[0].id,
      },
    }),
    prisma.farm.create({
      data: {
        name: 'Domaine Bio du Soleil',
        description: 'Agriculture biologique diversifiée',
        address: '456 Chemin du Bio, 84000 Avignon, France',
        latitude: 43.9493,
        longitude: 4.8055,
        totalArea: 85.2,
        ownerId: users[1].id,
      },
    }),
    prisma.farm.create({
      data: {
        name: 'Exploitation Moderne Tech',
        description: 'Ferme high-tech avec agriculture de précision',
        address: '789 Avenue Innovation, 31000 Toulouse, France',
        latitude: 43.6047,
        longitude: 1.4442,
        totalArea: 200.0,
        ownerId: users[0].id,
      },
    }),
  ]);
}

async function createFields(farms: any[]) {
  const fields = [];
  
  for (const farm of farms) {
    const farmFields = await prisma.$transaction([
      prisma.field.create({
        data: {
          name: 'Parcelle Nord',
          description: 'Parcelle principale orientée nord',
          area: 25.5,
          soilType: 'LOAMY',
          slope: 2.5,
          elevation: 120,
          coordinates: {
            type: 'Polygon',
            coordinates: [[[farm.longitude - 0.01, farm.latitude + 0.01], 
                          [farm.longitude + 0.01, farm.latitude + 0.01],
                          [farm.longitude + 0.01, farm.latitude - 0.01],
                          [farm.longitude - 0.01, farm.latitude - 0.01],
                          [farm.longitude - 0.01, farm.latitude + 0.01]]]
          },
          farmId: farm.id,
        },
      }),
      prisma.field.create({
        data: {
          name: 'Parcelle Sud',
          description: 'Parcelle secondaire orientée sud',
          area: 18.3,
          soilType: 'CLAY',
          slope: 1.8,
          elevation: 115,
          coordinates: {
            type: 'Polygon',
            coordinates: [[[farm.longitude - 0.005, farm.latitude - 0.005], 
                          [farm.longitude + 0.005, farm.latitude - 0.005],
                          [farm.longitude + 0.005, farm.latitude - 0.015],
                          [farm.longitude - 0.005, farm.latitude - 0.015],
                          [farm.longitude - 0.005, farm.latitude - 0.005]]]
          },
          farmId: farm.id,
        },
      }),
    ]);
    fields.push(...farmFields);
  }
  
  return fields;
}

async function createCrops(fields: any[]) {
  const crops = [];
  const cropTypes = ['CEREALS', 'VEGETABLES', 'FRUITS', 'LEGUMES'];
  const cropNames = ['Blé', 'Maïs', 'Tomates', 'Pommes de terre', 'Haricots verts'];
  
  for (let i = 0; i < fields.length; i++) {
    const field = fields[i];
    const cropType = cropTypes[i % cropTypes.length];
    const cropName = cropNames[i % cropNames.length];
    
    const crop = await prisma.crop.create({
      data: {
        name: cropName,
        variety: `Variété ${cropName} Premium`,
        cropType: cropType as any,
        plantingDate: new Date(2024, 3, 15 + i), // April 15 + i days
        expectedHarvestDate: new Date(2024, 8, 15 + i), // September 15 + i days
        expectedYield: 8.5 + (i * 0.5),
        fieldId: field.id,
      },
    });
    crops.push(crop);
  }
  
  return crops;
}

async function createSensors(farms: any[]) {
  const sensors = [];
  const sensorTypes = ['SOIL_MOISTURE', 'AIR_TEMPERATURE', 'SOIL_PH', 'RAINFALL'];
  
  for (const farm of farms) {
    for (let i = 0; i < 4; i++) {
      const sensor = await prisma.sensor.create({
        data: {
          name: `Capteur ${sensorTypes[i]} - ${farm.name}`,
          type: sensorTypes[i] as any,
          model: `Model-${sensorTypes[i]}-2024`,
          serialNumber: `SN${farm.id.slice(-4)}${i.toString().padStart(3, '0')}`,
          latitude: farm.latitude + (Math.random() - 0.5) * 0.01,
          longitude: farm.longitude + (Math.random() - 0.5) * 0.01,
          measurementInterval: 300,
          batteryLevel: 85 + Math.random() * 15,
          farmId: farm.id,
        },
      });
      sensors.push(sensor);
    }
  }
  
  return sensors;
}

async function createSensorReadings(sensors: any[]) {
  const readings = [];
  const now = new Date();
  
  for (const sensor of sensors) {
    // Create readings for the last 7 days
    for (let day = 0; day < 7; day++) {
      for (let hour = 0; hour < 24; hour += 4) { // Every 4 hours
        const timestamp = new Date(now);
        timestamp.setDate(timestamp.getDate() - day);
        timestamp.setHours(hour, 0, 0, 0);
        
        let value: number;
        let unit: string;
        
        switch (sensor.type) {
          case 'SOIL_MOISTURE':
            value = 30 + Math.random() * 40; // 30-70%
            unit = '%';
            break;
          case 'AIR_TEMPERATURE':
            value = 15 + Math.random() * 20; // 15-35°C
            unit = '°C';
            break;
          case 'SOIL_PH':
            value = 6.0 + Math.random() * 2; // 6.0-8.0
            unit = 'pH';
            break;
          case 'RAINFALL':
            value = Math.random() * 10; // 0-10mm
            unit = 'mm';
            break;
          default:
            value = Math.random() * 100;
            unit = 'unit';
        }
        
        readings.push({
          timestamp,
          value,
          unit,
          quality: 'GOOD' as any,
          sensorId: sensor.id,
        });
      }
    }
  }
  
  // Batch insert readings
  const batchSize = 100;
  for (let i = 0; i < readings.length; i += batchSize) {
    const batch = readings.slice(i, i + batchSize);
    await prisma.sensorReading.createMany({
      data: batch,
    });
  }
}

// Continue with other seed functions...
async function createWeatherStations(farms: any[]) {
  const stations = [];
  
  for (const farm of farms) {
    const station = await prisma.weatherStation.create({
      data: {
        name: `Station Météo ${farm.name}`,
        latitude: farm.latitude,
        longitude: farm.longitude,
        elevation: 120 + Math.random() * 50,
        isExternal: false,
        farmId: farm.id,
      },
    });
    stations.push(station);
  }
  
  return stations;
}

async function createWeatherData(stations: any[]) {
  const weatherData = [];
  const now = new Date();
  
  for (const station of stations) {
    // Create weather data for the last 30 days
    for (let day = 0; day < 30; day++) {
      for (let hour = 0; hour < 24; hour += 3) { // Every 3 hours
        const timestamp = new Date(now);
        timestamp.setDate(timestamp.getDate() - day);
        timestamp.setHours(hour, 0, 0, 0);
        
        weatherData.push({
          timestamp,
          temperature: 15 + Math.random() * 20, // 15-35°C
          humidity: 40 + Math.random() * 40, // 40-80%
          pressure: 1000 + Math.random() * 50, // 1000-1050 hPa
          windSpeed: Math.random() * 15, // 0-15 m/s
          windDirection: Math.random() * 360, // 0-360°
          rainfall: Math.random() * 5, // 0-5mm
          solarRadiation: Math.random() * 1000, // 0-1000 W/m²
          uvIndex: Math.random() * 10, // 0-10
          stationId: station.id,
        });
      }
    }
  }
  
  // Batch insert weather data
  const batchSize = 100;
  for (let i = 0; i < weatherData.length; i += batchSize) {
    const batch = weatherData.slice(i, i + batchSize);
    await prisma.weatherData.createMany({
      data: batch,
    });
  }
}

// Additional seed functions would continue here...
// For brevity, I'll add placeholders for the remaining functions

async function createSoilAnalyses(fields: any[]) {
  // Implementation for soil analyses
  console.log('Creating soil analyses...');
}

async function createTreatments(crops: any[]) {
  // Implementation for treatments
  console.log('Creating treatments...');
}

async function createIrrigationZones(fields: any[]) {
  // Implementation for irrigation zones
  console.log('Creating irrigation zones...');
  return [];
}

async function createIrrigationSchedules(zones: any[]) {
  // Implementation for irrigation schedules
  console.log('Creating irrigation schedules...');
}

async function createHarvests(crops: any[]) {
  // Implementation for harvests
  console.log('Creating harvests...');
}

async function createAlerts(users: any[]) {
  // Implementation for alerts
  console.log('Creating alerts...');
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}
