import {
  cn,
  formatNumber,
  formatCurrency,
  formatPercentage,
  formatDate,
  formatRelativeTime,
  truncateText,
  generateId,
  debounce,
  throttle,
  deepClone,
  isEmpty,
  formatBytes,
  convertArea,
  formatArea,
  convertTemperature,
  formatTemperature,
  getCropHealthColor,
  getSoilMoistureLevel,
  daysBetween,
  getGrowingSeasonProgress,
  formatYield,
  getWeatherCondition,
  isValidEmail,
  isValidPhoneNumber,
} from '@/lib/utils';

describe('Utils', () => {
  describe('cn (className merger)', () => {
    it('should merge class names correctly', () => {
      expect(cn('class1', 'class2')).toBe('class1 class2');
      expect(cn('class1', undefined, 'class2')).toBe('class1 class2');
      expect(cn('class1', false && 'class2', 'class3')).toBe('class1 class3');
    });

    it('should handle Tailwind conflicts', () => {
      expect(cn('p-4', 'p-2')).toBe('p-2');
      expect(cn('text-red-500', 'text-blue-500')).toBe('text-blue-500');
    });
  });

  describe('formatNumber', () => {
    it('should format numbers with French locale', () => {
      expect(formatNumber(1234.56)).toBe('1 234,56');
      expect(formatNumber(1000000)).toBe('1 000 000');
    });

    it('should handle custom options', () => {
      expect(formatNumber(1234.56, { maximumFractionDigits: 1 })).toBe('1 234,6');
    });
  });

  describe('formatCurrency', () => {
    it('should format currency in EUR by default', () => {
      expect(formatCurrency(1234.56)).toBe('1 234,56 €');
    });

    it('should handle different currencies', () => {
      expect(formatCurrency(1234.56, 'USD')).toContain('$');
    });
  });

  describe('formatPercentage', () => {
    it('should format percentages correctly', () => {
      expect(formatPercentage(0.1234)).toBe('12,3 %');
      expect(formatPercentage(0.1234, 2)).toBe('12,34 %');
    });
  });

  describe('formatDate', () => {
    it('should format dates with French locale', () => {
      const date = new Date('2024-06-15');
      const formatted = formatDate(date);
      expect(formatted).toContain('juin');
      expect(formatted).toContain('2024');
    });

    it('should handle string dates', () => {
      const formatted = formatDate('2024-06-15');
      expect(formatted).toContain('juin');
    });
  });

  describe('formatRelativeTime', () => {
    beforeAll(() => {
      jest.useFakeTimers();
      jest.setSystemTime(new Date('2024-06-15T12:00:00Z'));
    });

    afterAll(() => {
      jest.useRealTimers();
    });

    it('should format relative time correctly', () => {
      const oneHourAgo = new Date('2024-06-15T11:00:00Z');
      const result = formatRelativeTime(oneHourAgo);
      expect(result).toContain('heure');
    });
  });

  describe('truncateText', () => {
    it('should truncate long text', () => {
      const longText = 'This is a very long text that should be truncated';
      expect(truncateText(longText, 20)).toBe('This is a very long...');
    });

    it('should not truncate short text', () => {
      const shortText = 'Short text';
      expect(truncateText(shortText, 20)).toBe('Short text');
    });
  });

  describe('generateId', () => {
    it('should generate unique IDs', () => {
      const id1 = generateId();
      const id2 = generateId();
      expect(id1).not.toBe(id2);
      expect(id1).toHaveLength(9);
    });

    it('should include prefix when provided', () => {
      const id = generateId('test');
      expect(id).toMatch(/^test-/);
    });
  });

  describe('debounce', () => {
    beforeAll(() => {
      jest.useFakeTimers();
    });

    afterAll(() => {
      jest.useRealTimers();
    });

    it('should debounce function calls', () => {
      const mockFn = jest.fn();
      const debouncedFn = debounce(mockFn, 100);

      debouncedFn();
      debouncedFn();
      debouncedFn();

      expect(mockFn).not.toHaveBeenCalled();

      jest.advanceTimersByTime(100);
      expect(mockFn).toHaveBeenCalledTimes(1);
    });
  });

  describe('throttle', () => {
    beforeAll(() => {
      jest.useFakeTimers();
    });

    afterAll(() => {
      jest.useRealTimers();
    });

    it('should throttle function calls', () => {
      const mockFn = jest.fn();
      const throttledFn = throttle(mockFn, 100);

      throttledFn();
      throttledFn();
      throttledFn();

      expect(mockFn).toHaveBeenCalledTimes(1);

      jest.advanceTimersByTime(100);
      throttledFn();
      expect(mockFn).toHaveBeenCalledTimes(2);
    });
  });

  describe('deepClone', () => {
    it('should deep clone objects', () => {
      const original = { a: 1, b: { c: 2 } };
      const cloned = deepClone(original);

      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
      expect(cloned.b).not.toBe(original.b);
    });

    it('should handle arrays', () => {
      const original = [1, [2, 3], { a: 4 }];
      const cloned = deepClone(original);

      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
      expect(cloned[1]).not.toBe(original[1]);
    });

    it('should handle dates', () => {
      const original = new Date('2024-06-15');
      const cloned = deepClone(original);

      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
    });
  });

  describe('isEmpty', () => {
    it('should detect empty values', () => {
      expect(isEmpty(null)).toBe(true);
      expect(isEmpty(undefined)).toBe(true);
      expect(isEmpty('')).toBe(true);
      expect(isEmpty('   ')).toBe(true);
      expect(isEmpty([])).toBe(true);
      expect(isEmpty({})).toBe(true);
    });

    it('should detect non-empty values', () => {
      expect(isEmpty('text')).toBe(false);
      expect(isEmpty([1])).toBe(false);
      expect(isEmpty({ a: 1 })).toBe(false);
      expect(isEmpty(0)).toBe(false);
      expect(isEmpty(false)).toBe(false);
    });
  });

  describe('formatBytes', () => {
    it('should format bytes correctly', () => {
      expect(formatBytes(0)).toBe('0 Bytes');
      expect(formatBytes(1024)).toBe('1 KB');
      expect(formatBytes(1048576)).toBe('1 MB');
      expect(formatBytes(1073741824)).toBe('1 GB');
    });
  });

  describe('Agriculture-specific utilities', () => {
    describe('convertArea', () => {
      it('should convert hectares to other units', () => {
        expect(convertArea(1, 'acres')).toBeCloseTo(2.47105);
        expect(convertArea(1, 'm2')).toBe(10000);
        expect(convertArea(100, 'km2')).toBe(1);
      });
    });

    describe('formatArea', () => {
      it('should format area with correct units', () => {
        expect(formatArea(1.5)).toBe('1,5 ha');
        expect(formatArea(1, 'acres')).toContain('acres');
        expect(formatArea(1, 'm2')).toContain('m²');
      });
    });

    describe('convertTemperature', () => {
      it('should convert between Celsius and Fahrenheit', () => {
        expect(convertTemperature(0, 'C', 'F')).toBe(32);
        expect(convertTemperature(32, 'F', 'C')).toBe(0);
        expect(convertTemperature(25, 'C', 'C')).toBe(25);
      });
    });

    describe('formatTemperature', () => {
      it('should format temperature with unit', () => {
        expect(formatTemperature(25)).toBe('25°C');
        expect(formatTemperature(77, 'F')).toBe('77°F');
      });
    });

    describe('getCropHealthColor', () => {
      it('should return correct colors for health scores', () => {
        expect(getCropHealthColor(90)).toContain('green');
        expect(getCropHealthColor(70)).toContain('yellow');
        expect(getCropHealthColor(50)).toContain('orange');
        expect(getCropHealthColor(30)).toContain('red');
      });
    });

    describe('getSoilMoistureLevel', () => {
      it('should categorize soil moisture correctly', () => {
        expect(getSoilMoistureLevel(80).level).toBe('Élevé');
        expect(getSoilMoistureLevel(50).level).toBe('Optimal');
        expect(getSoilMoistureLevel(30).level).toBe('Faible');
        expect(getSoilMoistureLevel(10).level).toBe('Critique');
      });
    });

    describe('daysBetween', () => {
      it('should calculate days between dates', () => {
        const date1 = new Date('2024-06-15');
        const date2 = new Date('2024-06-20');
        expect(daysBetween(date1, date2)).toBe(5);
      });
    });

    describe('getGrowingSeasonProgress', () => {
      it('should calculate growing season progress', () => {
        const plantingDate = new Date('2024-04-01');
        const harvestDate = new Date('2024-10-01');
        
        // Mock current date to be halfway through season
        jest.useFakeTimers();
        jest.setSystemTime(new Date('2024-07-01'));
        
        const progress = getGrowingSeasonProgress(plantingDate, harvestDate);
        expect(progress).toBeGreaterThan(40);
        expect(progress).toBeLessThan(60);
        
        jest.useRealTimers();
      });
    });

    describe('formatYield', () => {
      it('should format yield with correct units', () => {
        expect(formatYield(8.5)).toBe('8,5 t/ha');
        expect(formatYield(8.5, 'kg/ha')).toBe('8 500 kg/ha');
        expect(formatYield(8.5, 'q/ha')).toBe('85 q/ha');
      });
    });

    describe('getWeatherCondition', () => {
      it('should return correct weather condition data', () => {
        const sunny = getWeatherCondition('sunny');
        expect(sunny.icon).toBe('☀️');
        expect(sunny.description).toBe('Ensoleillé');
        
        const rainy = getWeatherCondition('rainy');
        expect(rainy.icon).toBe('🌧️');
        expect(rainy.description).toBe('Pluvieux');
      });
    });
  });

  describe('Validation utilities', () => {
    describe('isValidEmail', () => {
      it('should validate email addresses', () => {
        expect(isValidEmail('<EMAIL>')).toBe(true);
        expect(isValidEmail('<EMAIL>')).toBe(true);
        expect(isValidEmail('invalid-email')).toBe(false);
        expect(isValidEmail('test@')).toBe(false);
        expect(isValidEmail('@example.com')).toBe(false);
      });
    });

    describe('isValidPhoneNumber', () => {
      it('should validate French phone numbers', () => {
        expect(isValidPhoneNumber('+33123456789')).toBe(true);
        expect(isValidPhoneNumber('0123456789')).toBe(true);
        expect(isValidPhoneNumber('01 23 45 67 89')).toBe(true);
        expect(isValidPhoneNumber('***********.89')).toBe(true);
        expect(isValidPhoneNumber('01-23-45-67-89')).toBe(true);
        expect(isValidPhoneNumber('123456789')).toBe(false);
        expect(isValidPhoneNumber('invalid')).toBe(false);
      });
    });
  });
});
