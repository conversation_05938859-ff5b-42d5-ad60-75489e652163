import { test, expect } from '@playwright/test';

test.describe('Homepage', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should display the main navigation', async ({ page }) => {
    // Check for logo and brand name
    await expect(page.locator('text=AIgricole')).toBeVisible();
    
    // Check for navigation links
    await expect(page.locator('text=Fonctionnalités')).toBeVisible();
    await expect(page.locator('text=Solutions')).toBeVisible();
    await expect(page.locator('text=Tarifs')).toBeVisible();
    await expect(page.locator('text=Contact')).toBeVisible();
    
    // Check for auth buttons
    await expect(page.locator('text=Connexion')).toBeVisible();
    await expect(page.locator('text=Commencer')).toBeVisible();
  });

  test('should display the hero section', async ({ page }) => {
    // Check for main heading
    await expect(page.locator('h1')).toContainText('Révolutionnez votre agriculture');
    
    // Check for subtitle
    await expect(page.locator('text=AIgricole combine intelligence artificielle')).toBeVisible();
    
    // Check for key benefits
    await expect(page.locator('text=+25% de rendement moyen')).toBeVisible();
    await expect(page.locator('text=-40% d\'intrants chimiques')).toBeVisible();
    await expect(page.locator('text=Fonctionne 100% hors ligne')).toBeVisible();
  });

  test('should display feature cards', async ({ page }) => {
    // Check for feature cards
    await expect(page.locator('text=IA Avancée')).toBeVisible();
    await expect(page.locator('text=Imagerie Satellite')).toBeVisible();
    await expect(page.locator('text=IoT Intelligent')).toBeVisible();
    await expect(page.locator('text=Offline-First')).toBeVisible();
    
    // Check for feature descriptions
    await expect(page.locator('text=Détection automatique des maladies')).toBeVisible();
    await expect(page.locator('text=Surveillance en temps réel')).toBeVisible();
    await expect(page.locator('text=Capteurs connectés')).toBeVisible();
    await expect(page.locator('text=Fonctionnement complet hors ligne')).toBeVisible();
  });

  test('should display solutions section', async ({ page }) => {
    // Check for solutions heading
    await expect(page.locator('text=Solutions adaptées à votre exploitation')).toBeVisible();
    
    // Check for solution types
    await expect(page.locator('text=Grandes Cultures')).toBeVisible();
    await expect(page.locator('text=Maraîchage')).toBeVisible();
    await expect(page.locator('text=Élevage')).toBeVisible();
    
    // Check for popular badge
    await expect(page.locator('text=Populaire')).toBeVisible();
  });

  test('should have working navigation links', async ({ page }) => {
    // Test features link
    await page.click('text=Fonctionnalités');
    await expect(page).toHaveURL('/#features');
    
    // Test solutions link
    await page.click('text=Solutions');
    await expect(page).toHaveURL('/#solutions');
  });

  test('should have working CTA buttons', async ({ page }) => {
    // Test register button
    const registerButton = page.locator('text=Commencer').first();
    await expect(registerButton).toHaveAttribute('href', '/auth/register');
    
    // Test login button
    const loginButton = page.locator('text=Connexion');
    await expect(loginButton).toHaveAttribute('href', '/auth/login');
  });

  test('should display footer', async ({ page }) => {
    // Scroll to footer
    await page.locator('footer').scrollIntoViewIfNeeded();
    
    // Check for footer content
    await expect(page.locator('text=© 2024 AIgricole. Tous droits réservés.')).toBeVisible();
    
    // Check for footer sections
    await expect(page.locator('text=Produit')).toBeVisible();
    await expect(page.locator('text=Support')).toBeVisible();
    await expect(page.locator('text=Entreprise')).toBeVisible();
  });

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check that content is still visible
    await expect(page.locator('text=AIgricole')).toBeVisible();
    await expect(page.locator('h1')).toContainText('Révolutionnez votre agriculture');
    
    // Check that navigation might be collapsed (hamburger menu)
    // This depends on your actual mobile navigation implementation
  });

  test('should have proper accessibility', async ({ page }) => {
    // Check for proper heading hierarchy
    const h1 = page.locator('h1');
    await expect(h1).toBeVisible();
    
    // Check for alt text on images (if any)
    const images = page.locator('img');
    const imageCount = await images.count();
    
    for (let i = 0; i < imageCount; i++) {
      const img = images.nth(i);
      await expect(img).toHaveAttribute('alt');
    }
    
    // Check for proper link text
    const links = page.locator('a');
    const linkCount = await links.count();
    
    for (let i = 0; i < linkCount; i++) {
      const link = links.nth(i);
      const text = await link.textContent();
      const ariaLabel = await link.getAttribute('aria-label');
      
      // Links should have either text content or aria-label
      expect(text || ariaLabel).toBeTruthy();
    }
  });

  test('should load performance metrics within acceptable limits', async ({ page }) => {
    // Navigate to page and wait for load
    const startTime = Date.now();
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;
    
    // Check that page loads within 3 seconds
    expect(loadTime).toBeLessThan(3000);
    
    // Check for Core Web Vitals (simplified)
    const performanceEntries = await page.evaluate(() => {
      return JSON.stringify(performance.getEntriesByType('navigation'));
    });
    
    const entries = JSON.parse(performanceEntries);
    if (entries.length > 0) {
      const entry = entries[0];
      
      // Check that DOM content loads quickly
      expect(entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart).toBeLessThan(1000);
    }
  });

  test('should handle errors gracefully', async ({ page }) => {
    // Test with network offline
    await page.context().setOffline(true);
    
    // Try to navigate
    const response = await page.goto('/', { waitUntil: 'networkidle' }).catch(() => null);
    
    // Should handle offline gracefully
    if (response) {
      expect(response.status()).not.toBe(200);
    }
    
    // Restore network
    await page.context().setOffline(false);
  });

  test('should have working demo video/interaction', async ({ page }) => {
    // Look for demo button or video
    const demoButton = page.locator('text=Voir la démo');
    
    if (await demoButton.isVisible()) {
      await demoButton.click();
      
      // Check that something happens (modal opens, video plays, etc.)
      // This depends on your actual demo implementation
      await expect(page.locator('[data-testid="demo-modal"], video, iframe')).toBeVisible();
    }
  });

  test('should display trust indicators', async ({ page }) => {
    // Check for customer count
    await expect(page.locator('text=10,000 agriculteurs')).toBeVisible();
    
    // Check for customer logos (if any)
    const logoSection = page.locator('text=Déjà adopté par').locator('..');
    if (await logoSection.isVisible()) {
      // Should have some customer logos or placeholders
      await expect(logoSection.locator('div, img').first()).toBeVisible();
    }
  });
});
