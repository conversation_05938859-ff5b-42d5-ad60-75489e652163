'use client';

import * as React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ArrowR<PERSON>, Play, Sparkles, TrendingUp, Shield, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export function HeroSection() {
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 py-24 sm:py-32">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-field-pattern opacity-5" />
      
      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-primary/10 rounded-full blur-xl animate-pulse" />
      <div className="absolute top-40 right-20 w-32 h-32 bg-secondary/10 rounded-full blur-xl animate-pulse delay-1000" />
      <div className="absolute bottom-20 left-1/4 w-16 h-16 bg-accent/10 rounded-full blur-xl animate-pulse delay-2000" />

      <div className="container relative">
        <div className="mx-auto max-w-4xl text-center">
          {/* Announcement Badge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-8"
          >
            <Badge variant="secondary" className="px-4 py-2 text-sm">
              <Sparkles className="w-4 h-4 mr-2" />
              Nouveau : IA Prédictive v2.0 disponible
            </Badge>
          </motion.div>

          {/* Main Heading */}
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl lg:text-7xl"
          >
            Révolutionnez votre{' '}
            <span className="text-gradient bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              agriculture
            </span>{' '}
            avec l'IA
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mt-6 text-lg leading-8 text-gray-600 sm:text-xl max-w-3xl mx-auto"
          >
            AIgricole combine intelligence artificielle, IoT et imagerie satellitaire pour optimiser 
            vos rendements, réduire vos coûts et prendre les meilleures décisions pour vos cultures.
          </motion.p>

          {/* Key Benefits */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="mt-8 flex flex-wrap justify-center gap-6 text-sm text-gray-600"
          >
            <div className="flex items-center">
              <TrendingUp className="w-4 h-4 text-primary mr-2" />
              +25% de rendement moyen
            </div>
            <div className="flex items-center">
              <Shield className="w-4 h-4 text-primary mr-2" />
              -40% d'intrants chimiques
            </div>
            <div className="flex items-center">
              <Zap className="w-4 h-4 text-primary mr-2" />
              Fonctionne 100% hors ligne
            </div>
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mt-10 flex flex-col sm:flex-row items-center justify-center gap-4"
          >
            <Button size="lg" className="px-8 py-4 text-lg" asChild>
              <Link href="/auth/register">
                Commencer gratuitement
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            
            <Button size="lg" variant="outline" className="px-8 py-4 text-lg" asChild>
              <Link href="#demo">
                <Play className="mr-2 h-5 w-5" />
                Voir la démo
              </Link>
            </Button>
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="mt-12 text-center"
          >
            <p className="text-sm text-gray-500 mb-6">
              Déjà adopté par plus de 10,000 agriculteurs en France
            </p>
            
            {/* Customer Logos */}
            <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
              {/* Placeholder for customer logos */}
              <div className="h-8 w-24 bg-gray-200 rounded" />
              <div className="h-8 w-20 bg-gray-200 rounded" />
              <div className="h-8 w-28 bg-gray-200 rounded" />
              <div className="h-8 w-22 bg-gray-200 rounded" />
              <div className="h-8 w-26 bg-gray-200 rounded" />
            </div>
          </motion.div>
        </div>

        {/* Hero Image/Video */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mt-16 relative"
        >
          <div className="relative mx-auto max-w-5xl">
            {/* Main Dashboard Preview */}
            <div className="relative rounded-xl bg-white shadow-2xl ring-1 ring-gray-900/10 overflow-hidden">
              <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-400 rounded-full" />
                  <div className="w-3 h-3 bg-yellow-400 rounded-full" />
                  <div className="w-3 h-3 bg-green-400 rounded-full" />
                  <div className="ml-4 text-sm text-gray-600">app.aigricole.com</div>
                </div>
              </div>
              
              {/* Dashboard Content Preview */}
              <div className="p-6 bg-gradient-to-br from-green-50 to-blue-50 min-h-[400px]">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Weather Card */}
                  <div className="bg-white rounded-lg p-4 shadow-sm">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold text-gray-900">Météo</h3>
                      <span className="text-2xl">☀️</span>
                    </div>
                    <div className="text-2xl font-bold text-gray-900">24°C</div>
                    <div className="text-sm text-gray-600">Ensoleillé</div>
                  </div>

                  {/* Crop Health Card */}
                  <div className="bg-white rounded-lg p-4 shadow-sm">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold text-gray-900">Santé Cultures</h3>
                      <div className="w-3 h-3 bg-green-500 rounded-full" />
                    </div>
                    <div className="text-2xl font-bold text-green-600">92%</div>
                    <div className="text-sm text-gray-600">Excellent état</div>
                  </div>

                  {/* Irrigation Card */}
                  <div className="bg-white rounded-lg p-4 shadow-sm">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold text-gray-900">Irrigation</h3>
                      <span className="text-2xl">💧</span>
                    </div>
                    <div className="text-2xl font-bold text-blue-600">65%</div>
                    <div className="text-sm text-gray-600">Humidité sol</div>
                  </div>
                </div>

                {/* Chart Preview */}
                <div className="mt-6 bg-white rounded-lg p-4 shadow-sm">
                  <h3 className="font-semibold text-gray-900 mb-4">Évolution des Rendements</h3>
                  <div className="h-32 bg-gradient-to-r from-green-100 to-blue-100 rounded flex items-end justify-between px-4 pb-4">
                    {[40, 65, 45, 80, 70, 90, 85].map((height, i) => (
                      <div
                        key={i}
                        className="bg-primary rounded-t w-8"
                        style={{ height: `${height}%` }}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Floating Cards */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 1 }}
              className="absolute -left-4 top-20 hidden lg:block"
            >
              <div className="bg-white rounded-lg shadow-lg p-4 max-w-xs">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                    <span className="text-red-600 font-semibold">!</span>
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">Alerte Maladie</div>
                    <div className="text-sm text-gray-600">Mildiou détecté - Parcelle Nord</div>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 1.2 }}
              className="absolute -right-4 bottom-20 hidden lg:block"
            >
              <div className="bg-white rounded-lg shadow-lg p-4 max-w-xs">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-green-600 font-semibold">✓</span>
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">Irrigation Optimisée</div>
                    <div className="text-sm text-gray-600">-30% d'eau économisée</div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
