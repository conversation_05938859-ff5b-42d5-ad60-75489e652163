'use client';

import * as React from 'react';
import { motion } from 'framer-motion';
import { 
  Brain, 
  Satellite, 
  Wifi, 
  CloudOff, 
  BarChart3, 
  Shield, 
  Smartphone,
  Zap,
  Eye,
  Droplets,
  Thermometer,
  Activity
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const features = [
  {
    icon: Brain,
    title: 'Intelligence Artificielle Avancée',
    description: 'Modèles d\'IA spécialisés pour l\'agriculture avec détection automatique des maladies, prédiction des rendements et recommandations personnalisées.',
    benefits: ['95% de précision', 'Détection précoce', 'Recommandations sur mesure'],
    category: 'IA',
  },
  {
    icon: Satellite,
    title: 'Imagerie Satellitaire & Drones',
    description: 'Surveillance en temps réel de vos parcelles avec analyse NDVI, détection des anomalies et cartographie automatique.',
    benefits: ['Images haute résolution', 'Analyse spectrale', 'Historique complet'],
    category: 'Imagerie',
  },
  {
    icon: Wifi,
    title: 'IoT Intelligent',
    description: 'Réseau de capteurs connectés pour surveiller sol, météo et cultures avec alertes automatiques et edge computing.',
    benefits: ['Capteurs autonomes', 'Alertes temps réel', 'Maintenance prédictive'],
    category: 'IoT',
  },
  {
    icon: CloudOff,
    title: 'Fonctionnement Offline',
    description: 'Application 100% fonctionnelle hors ligne avec synchronisation intelligente pour le travail terrain.',
    benefits: ['Zéro dépendance réseau', 'Sync automatique', 'Données sécurisées'],
    category: 'Offline',
  },
  {
    icon: BarChart3,
    title: 'Analytics & Reporting',
    description: 'Tableaux de bord interactifs avec métriques ROI, comparaisons historiques et exports automatisés.',
    benefits: ['ROI en temps réel', 'Benchmarking', 'Rapports automatiques'],
    category: 'Analytics',
  },
  {
    icon: Shield,
    title: 'Sécurité & Conformité',
    description: 'Chiffrement end-to-end, conformité RGPD et sauvegarde automatique de toutes vos données agricoles.',
    benefits: ['Chiffrement AES-256', 'Conformité RGPD', 'Backup automatique'],
    category: 'Sécurité',
  },
];

const detailedFeatures = [
  {
    icon: Eye,
    title: 'Vision par Ordinateur',
    description: 'Analyse automatique des images de cultures pour détecter maladies, ravageurs et stress nutritionnel.',
    image: '/images/computer-vision-demo.jpg',
    stats: [
      { label: 'Maladies détectées', value: '50+' },
      { label: 'Précision', value: '95%' },
      { label: 'Temps de traitement', value: '<30s' },
    ],
  },
  {
    icon: Droplets,
    title: 'Irrigation de Précision',
    description: 'Optimisation automatique de l\'irrigation basée sur les données sol, météo et besoins des cultures.',
    image: '/images/irrigation-demo.jpg',
    stats: [
      { label: 'Économie d\'eau', value: '40%' },
      { label: 'Zones gérées', value: '1000+' },
      { label: 'Précision', value: '±2%' },
    ],
  },
  {
    icon: Activity,
    title: 'Prédictions Avancées',
    description: 'Modèles prédictifs pour rendements, maladies, météo et optimisation des interventions.',
    image: '/images/predictions-demo.jpg',
    stats: [
      { label: 'Précision prédiction', value: '90%' },
      { label: 'Horizon temporel', value: '30 jours' },
      { label: 'Modèles actifs', value: '25+' },
    ],
  },
];

export function FeaturesSection() {
  return (
    <section id="features" className="py-24 bg-white">
      <div className="container">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <Badge variant="secondary" className="mb-4">
              Fonctionnalités Avancées
            </Badge>
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
              Tout ce dont vous avez besoin pour une agriculture intelligente
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Une suite complète d'outils alimentés par l'IA pour optimiser chaque aspect 
              de votre exploitation agricole.
            </p>
          </motion.div>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-24">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                <CardHeader>
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                      <feature.icon className="w-5 h-5 text-primary" />
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {feature.category}
                    </Badge>
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, i) => (
                      <li key={i} className="flex items-center text-sm text-muted-foreground">
                        <div className="w-1.5 h-1.5 bg-primary rounded-full mr-3" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Detailed Features */}
        <div className="space-y-24">
          {detailedFeatures.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${
                index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
              }`}
            >
              {/* Content */}
              <div className={index % 2 === 1 ? 'lg:col-start-2' : ''}>
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                    <feature.icon className="w-6 h-6 text-primary" />
                  </div>
                  <Badge variant="secondary">Fonctionnalité Phare</Badge>
                </div>
                
                <h3 className="text-3xl font-bold mb-4">{feature.title}</h3>
                <p className="text-lg text-muted-foreground mb-8">
                  {feature.description}
                </p>

                {/* Stats */}
                <div className="grid grid-cols-3 gap-6">
                  {feature.stats.map((stat, i) => (
                    <div key={i} className="text-center">
                      <div className="text-2xl font-bold text-primary mb-1">
                        {stat.value}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {stat.label}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Image/Demo */}
              <div className={index % 2 === 1 ? 'lg:col-start-1' : ''}>
                <div className="relative">
                  <div className="aspect-video bg-gradient-to-br from-green-100 to-blue-100 rounded-xl overflow-hidden shadow-lg">
                    {/* Placeholder for actual demo/image */}
                    <div className="w-full h-full flex items-center justify-center">
                      <div className="text-center">
                        <feature.icon className="w-16 h-16 text-primary mx-auto mb-4" />
                        <div className="text-lg font-semibold text-gray-700">
                          Démo Interactive
                        </div>
                        <div className="text-sm text-gray-500">
                          {feature.title}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Floating Elements */}
                  <div className="absolute -top-4 -right-4 w-8 h-8 bg-primary/20 rounded-full animate-pulse" />
                  <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-secondary/20 rounded-full animate-pulse delay-1000" />
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Integration Section */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-24 text-center"
        >
          <h3 className="text-2xl font-bold mb-8">Intégrations & Compatibilité</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center opacity-60">
            {/* Integration logos placeholders */}
            {[
              'John Deere',
              'Case IH',
              'New Holland',
              'Fendt',
              'Claas',
              'Massey Ferguson'
            ].map((brand, i) => (
              <div key={i} className="text-center">
                <div className="h-12 w-20 bg-gray-200 rounded mx-auto mb-2" />
                <div className="text-xs text-gray-500">{brand}</div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
