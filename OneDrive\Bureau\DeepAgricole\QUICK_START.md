# 🚀 Guide de Démarrage Rapide - AIgricole

Ce guide vous permettra de démarrer rapidement avec AIgricole en quelques minutes.

## ⚡ Installation Express (5 minutes)

### 1. P<PERSON>requis
```bash
# Vérifiez vos versions
node --version  # ≥ 18.0.0
npm --version   # ≥ 9.0.0
docker --version # ≥ 20.0.0 (optionnel mais recommandé)
```

### 2. Installation Automatique
```bash
# Clonez le projet
git clone https://github.com/aigricole/aigricole.git
cd aigricole

# Installation et configuration automatique
npm run setup
```

Le script `setup` va :
- ✅ Installer toutes les dépendances
- ✅ Configurer les variables d'environnement
- ✅ Démarrer les services Docker (PostgreSQL, Redis)
- ✅ Configurer la base de données
- ✅ Insérer des données de test
- ✅ Construire les packages partagés

### 3. Démarrage
```bash
# Démarrer l'environnement de développement
npm run dev
```

🎉 **C'est tout !** Votre application est maintenant accessible sur :
- 🌐 **Dashboard Web** : http://localhost:3000
- 📊 **Base de données** : http://localhost:5555 (Prisma Studio)

## 🛠️ Installation Manuelle (si nécessaire)

Si l'installation automatique échoue, suivez ces étapes :

### 1. Dépendances
```bash
npm install
```

### 2. Services Docker
```bash
# Démarrer PostgreSQL et Redis
docker-compose up -d postgres redis

# Ou installez-les manuellement sur votre système
```

### 3. Configuration
```bash
# Copiez les fichiers d'environnement
cp packages/database/.env.example packages/database/.env
cp apps/web/.env.example apps/web/.env.local

# Éditez les fichiers .env avec vos paramètres
```

### 4. Base de données
```bash
# Générer le client Prisma
npm run db:generate

# Exécuter les migrations
npm run db:migrate

# Insérer les données de test
npm run db:seed
```

### 5. Construction
```bash
# Construire les packages partagés
npm run build:packages
```

## 🎯 Premiers Pas

### Connexion
1. Allez sur http://localhost:3000
2. Cliquez sur "Connexion"
3. Utilisez les identifiants de test :
   - **Email** : `<EMAIL>`
   - **Mot de passe** : `TestPassword123!`

### Exploration
- 🏠 **Dashboard** : Vue d'ensemble de votre exploitation
- 🌾 **Cultures** : Gestion de vos cultures et parcelles
- 📡 **Capteurs** : Surveillance IoT en temps réel
- 🛰️ **Imagerie** : Analyse satellite de vos parcelles
- 📊 **Analytics** : Rapports et prédictions IA

## 🧪 Tests

### Tests Unitaires
```bash
npm run test              # Tous les tests
npm run test:unit         # Tests unitaires uniquement
npm run test:coverage     # Avec couverture de code
```

### Tests E2E
```bash
npm run test:e2e          # Tests end-to-end
npm run test:e2e:ui       # Interface graphique
```

### Tests Spécifiques Agriculture
```bash
npm run test:agriculture  # Tests métier agricoles
npm run test:ai-models    # Tests des modèles IA
```

## 📦 Structure du Projet

```
DeepAgricole/
├── apps/
│   ├── web/              # 🌐 Dashboard Next.js
│   ├── mobile/           # 📱 App React Native
│   └── api/              # 🔌 API Backend
├── packages/
│   ├── database/         # 🗄️ Prisma + PostgreSQL
│   ├── ai-models/        # 🤖 Modèles IA
│   ├── shared-ui/        # 🎨 Composants UI
│   ├── iot-sdk/          # 📡 SDK IoT
│   └── offline-sync/     # 📴 Synchronisation
├── services/             # 🔧 Microservices
├── tests/                # 🧪 Tests
└── deployment/           # 🚀 Déploiement
```

## 🔧 Commandes Utiles

### Développement
```bash
npm run dev               # Démarrer tout
npm run dev:web           # Web uniquement
npm run dev:mobile        # Mobile uniquement
npm run dev -- --db-studio # Avec Prisma Studio
```

### Base de Données
```bash
npm run db:studio         # Interface graphique
npm run db:migrate        # Nouvelles migrations
npm run db:reset          # Reset complet
npm run db:seed           # Données de test
```

### Qualité du Code
```bash
npm run lint              # ESLint
npm run format            # Prettier
npm run type-check        # TypeScript
```

### IA et Modèles
```bash
npm run train:models      # Entraîner les modèles IA
npm run evaluate:models   # Évaluer les performances
npm run export:models     # Exporter pour production
```

## 🌱 Données de Test

Le projet inclut des données de test réalistes :

- **👤 Utilisateurs** : Agriculteurs, agronomes, administrateurs
- **🏡 Exploitations** : Différents types (céréales, maraîchage, élevage)
- **🌾 Cultures** : Blé, maïs, tomates, etc.
- **📡 Capteurs** : Humidité sol, température, météo
- **📊 Données** : Historiques de rendements, analyses sol

## 🆘 Dépannage

### Problèmes Courants

**❌ Erreur de connexion à la base de données**
```bash
# Vérifiez que PostgreSQL fonctionne
docker-compose ps postgres

# Redémarrez si nécessaire
docker-compose restart postgres
```

**❌ Port déjà utilisé**
```bash
# Trouvez le processus utilisant le port 3000
lsof -i :3000

# Tuez le processus ou changez le port
PORT=3001 npm run dev
```

**❌ Erreur de permissions**
```bash
# Sur Linux/Mac, ajoutez sudo si nécessaire
sudo npm install

# Ou changez le propriétaire
sudo chown -R $USER:$USER node_modules
```

### Logs et Debug
```bash
# Logs détaillés
DEBUG=* npm run dev

# Logs base de données
npm run db:logs

# Logs des tests
npm run test -- --verbose
```

## 📚 Ressources

- 📖 **[Documentation Complète](docs/README.md)**
- 🏗️ **[Guide Architecture](docs/architecture.md)**
- 🔌 **[API Reference](docs/api-reference.md)**
- 🤝 **[Guide Contribution](CONTRIBUTING.md)**

## 💬 Support

- 🐛 **Issues** : [GitHub Issues](https://github.com/aigricole/aigricole/issues)
- 💬 **Discord** : [Communauté AIgricole](https://discord.gg/aigricole)
- 📧 **Email** : <EMAIL>

---

🌱 **Bon développement avec AIgricole !**
