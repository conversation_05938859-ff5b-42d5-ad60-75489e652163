const http = require('http');
const url = require('url');
const path = require('path');

const PORT = process.env.PORT || 3000;

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);

  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle API proxy
  if (parsedUrl.pathname.startsWith('/api')) {
    // Simple proxy to backend
    const backendUrl = `http://localhost:8000${parsedUrl.pathname}${parsedUrl.search || ''}`;

    const options = {
      hostname: 'localhost',
      port: 8000,
      path: parsedUrl.pathname + (parsedUrl.search || ''),
      method: req.method,
      headers: req.headers
    };

    const proxyReq = http.request(options, (proxyRes) => {
      res.writeHead(proxyRes.statusCode, proxyRes.headers);
      proxyRes.pipe(res);
    });

    proxyReq.on('error', (err) => {
      res.writeHead(500);
      res.end('Backend connection error');
    });

    req.pipe(proxyReq);
    return;
  }

  // Serve HTML page
  res.writeHead(200, { 'Content-Type': 'text/html' });
  res.send(`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIgricole - Agricultural AI Platform</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@mui/material@latest/umd/material-ui.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
    <style>
        body {
            margin: 0;
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 90%;
            text-align: center;
        }
        .logo {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .title {
            color: #2e7d32;
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        .subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }
        .status {
            background: #f1f8e9;
            border: 1px solid #c8e6c9;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .status-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 0.5rem 0;
        }
        .status-ok {
            color: #2e7d32;
            font-weight: 600;
        }
        .btn {
            background: #2e7d32;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0.5rem;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #1b5e20;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .feature {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #2e7d32;
        }
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🌱</div>
        <h1 class="title">AIgricole</h1>
        <p class="subtitle">Plateforme d'IA Agricole Avancée</p>
        
        <div class="status">
            <h3>État du Système</h3>
            <div class="status-item">
                <span>Backend Python FastAPI</span>
                <span class="status-ok" id="backend-status">⏳ Vérification...</span>
            </div>
            <div class="status-item">
                <span>Frontend React</span>
                <span class="status-ok">✅ Actif</span>
            </div>
        </div>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">🤖</div>
                <h4>IA Avancée</h4>
                <p>Détection de maladies, prédiction de rendement</p>
            </div>
            <div class="feature">
                <div class="feature-icon">📡</div>
                <h4>IoT Intégré</h4>
                <p>Capteurs en temps réel, monitoring automatique</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🛰️</div>
                <h4>Imagerie Satellite</h4>
                <p>Analyse des cultures par satellite</p>
            </div>
            <div class="feature">
                <div class="feature-icon">📊</div>
                <h4>Analytics</h4>
                <p>Tableaux de bord et rapports détaillés</p>
            </div>
        </div>

        <div>
            <button class="btn" onclick="testBackend()">Tester le Backend</button>
            <button class="btn" onclick="openDocs()">Documentation API</button>
            <button class="btn" onclick="viewDashboard()">Tableau de Bord</button>
        </div>
    </div>

    <script>
        // Test backend connection
        async function testBackend() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                document.getElementById('backend-status').innerHTML = '✅ Actif';
                alert('Backend connecté avec succès!\\n' + JSON.stringify(data, null, 2));
            } catch (error) {
                document.getElementById('backend-status').innerHTML = '❌ Erreur';
                alert('Erreur de connexion au backend: ' + error.message);
            }
        }

        function openDocs() {
            window.open('http://localhost:8000/docs', '_blank');
        }

        function viewDashboard() {
            alert('Tableau de bord en cours de développement!');
        }

        // Auto-test backend on load
        window.onload = function() {
            setTimeout(testBackend, 1000);
        };
    </script>
</body>
</html>
  `);
});

server.listen(PORT, () => {
  console.log(`🌱 AIgricole Frontend running on http://localhost:${PORT}`);
  console.log(`📡 Proxying API calls to http://localhost:8000`);
});
