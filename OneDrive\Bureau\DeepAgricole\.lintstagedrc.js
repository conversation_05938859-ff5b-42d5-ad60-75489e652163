module.exports = {
  // TypeScript and JavaScript files
  '**/*.{ts,tsx,js,jsx}': [
    'eslint --fix',
    'prettier --write',
    'git add',
  ],

  // JSON files
  '**/*.json': [
    'prettier --write',
    'git add',
  ],

  // Markdown files
  '**/*.md': [
    'prettier --write',
    'git add',
  ],

  // CSS and styling files
  '**/*.{css,scss,sass}': [
    'prettier --write',
    'git add',
  ],

  // YAML files
  '**/*.{yml,yaml}': [
    'prettier --write',
    'git add',
  ],

  // Package.json files - validate and format
  '**/package.json': [
    'prettier --write',
    'git add',
  ],

  // Prisma schema files
  '**/schema.prisma': [
    'npx prisma format',
    'git add',
  ],

  // Test files - run tests for changed test files
  '**/*.{test,spec}.{ts,tsx,js,jsx}': [
    'npm run test:file',
  ],

  // Database migration files - validate
  '**/migrations/**/*.sql': [
    'echo "🔍 Validating database migration..."',
  ],

  // Docker files
  '**/Dockerfile*': [
    'echo "🐳 Docker file changed - consider running docker build test"',
  ],

  // Kubernetes manifests
  '**/*.{yaml,yml}': [
    'echo "☸️ Kubernetes manifest changed - consider validation"',
  ],

  // Environment files - security check
  '**/.env*': [
    'echo "⚠️ Environment file changed - ensure no secrets are committed"',
  ],
};
