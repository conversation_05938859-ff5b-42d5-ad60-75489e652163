#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, description) {
  log(`\n🔄 ${description}...`, 'blue');
  try {
    execSync(command, { stdio: 'inherit' });
    log(`✅ ${description} completed!`, 'green');
  } catch (error) {
    log(`❌ Error during ${description}: ${error.message}`, 'red');
    process.exit(1);
  }
}

function createEnvFile(templatePath, targetPath, variables = {}) {
  if (fs.existsSync(targetPath)) {
    log(`⚠️  ${targetPath} already exists, skipping...`, 'yellow');
    return;
  }

  if (!fs.existsSync(templatePath)) {
    log(`⚠️  Template ${templatePath} not found, skipping...`, 'yellow');
    return;
  }

  let content = fs.readFileSync(templatePath, 'utf8');
  
  // Replace variables in template
  Object.entries(variables).forEach(([key, value]) => {
    content = content.replace(new RegExp(`{{${key}}}`, 'g'), value);
  });

  fs.writeFileSync(targetPath, content);
  log(`✅ Created ${targetPath}`, 'green');
}

async function promptUser(question, defaultValue = '') {
  return new Promise((resolve) => {
    const prompt = defaultValue 
      ? `${question} (${defaultValue}): `
      : `${question}: `;
    
    rl.question(prompt, (answer) => {
      resolve(answer.trim() || defaultValue);
    });
  });
}

async function main() {
  log('🌱 Welcome to AIgricole Setup!', 'green');
  log('This script will help you set up your development environment.\n', 'cyan');

  // Check Node.js version
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 18) {
    log('❌ Node.js 18 or higher is required!', 'red');
    log(`Current version: ${nodeVersion}`, 'yellow');
    process.exit(1);
  }
  
  log(`✅ Node.js version: ${nodeVersion}`, 'green');

  // Collect configuration
  log('\n📝 Configuration Setup', 'magenta');
  
  const config = {
    dbHost: await promptUser('Database host', 'localhost'),
    dbPort: await promptUser('Database port', '5432'),
    dbName: await promptUser('Database name', 'aigricole_dev'),
    dbUser: await promptUser('Database user', 'aigricole'),
    dbPassword: await promptUser('Database password', 'aigricole_dev_password'),
    redisUrl: await promptUser('Redis URL', 'redis://localhost:6379'),
    jwtSecret: await promptUser('JWT Secret', 'your-super-secret-jwt-key-change-in-production'),
    openWeatherApiKey: await promptUser('OpenWeather API Key (optional)', ''),
    sentinelHubClientId: await promptUser('Sentinel Hub Client ID (optional)', ''),
    sentinelHubClientSecret: await promptUser('Sentinel Hub Client Secret (optional)', ''),
  };

  rl.close();

  // Install dependencies
  execCommand('npm install', 'Installing dependencies');

  // Setup Husky
  execCommand('npx husky install', 'Setting up Git hooks');

  // Create environment files
  log('\n📄 Creating environment files...', 'blue');
  
  const databaseUrl = `postgresql://${config.dbUser}:${config.dbPassword}@${config.dbHost}:${config.dbPort}/${config.dbName}?schema=public`;
  const testDatabaseUrl = `postgresql://${config.dbUser}:${config.dbPassword}@${config.dbHost}:${config.dbPort}/${config.dbName}_test?schema=public`;

  // Database package env
  createEnvFile(
    'packages/database/.env.example',
    'packages/database/.env',
    {
      DATABASE_URL: databaseUrl,
      TEST_DATABASE_URL: testDatabaseUrl,
      REDIS_URL: config.redisUrl,
      JWT_SECRET: config.jwtSecret,
      OPENWEATHER_API_KEY: config.openWeatherApiKey,
      SENTINEL_HUB_CLIENT_ID: config.sentinelHubClientId,
      SENTINEL_HUB_CLIENT_SECRET: config.sentinelHubClientSecret,
    }
  );

  // Web app env
  createEnvFile(
    'apps/web/.env.example',
    'apps/web/.env.local',
    {
      DATABASE_URL: databaseUrl,
      NEXTAUTH_SECRET: config.jwtSecret,
      NEXTAUTH_URL: 'http://localhost:3000',
    }
  );

  // Check if Docker is available
  try {
    execSync('docker --version', { stdio: 'ignore' });
    log('\n🐳 Docker detected!', 'green');
    
    const useDocker = await new Promise((resolve) => {
      const rl2 = readline.createInterface({
        input: process.stdin,
        output: process.stdout,
      });
      
      rl2.question('Do you want to start services with Docker? (y/N): ', (answer) => {
        rl2.close();
        resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
      });
    });

    if (useDocker) {
      execCommand('docker-compose up -d postgres redis', 'Starting database services with Docker');
    }
  } catch (error) {
    log('⚠️  Docker not found. You\'ll need to set up PostgreSQL and Redis manually.', 'yellow');
  }

  // Database setup
  log('\n🗄️  Setting up database...', 'blue');
  
  try {
    execCommand('npm run db:generate', 'Generating Prisma client');
    execCommand('npm run db:migrate', 'Running database migrations');
    execCommand('npm run db:seed', 'Seeding database with sample data');
  } catch (error) {
    log('⚠️  Database setup failed. Make sure PostgreSQL is running and accessible.', 'yellow');
    log('You can run the database setup later with: npm run db:setup', 'cyan');
  }

  // Build packages
  execCommand('npm run build:packages', 'Building shared packages');

  // Final checks
  log('\n🔍 Running final checks...', 'blue');
  
  try {
    execCommand('npm run lint', 'Running linter');
    execCommand('npm run type-check', 'Type checking');
    execCommand('npm run test:quick', 'Running quick tests');
  } catch (error) {
    log('⚠️  Some checks failed. You can fix them later.', 'yellow');
  }

  // Success message
  log('\n🎉 Setup completed successfully!', 'green');
  log('\n📚 Next steps:', 'cyan');
  log('1. Start the development server: npm run dev', 'white');
  log('2. Open http://localhost:3000 in your browser', 'white');
  log('3. Check the documentation: docs/README.md', 'white');
  log('4. Join our Discord: https://discord.gg/aigricole', 'white');
  
  log('\n🌱 Happy farming with AI!', 'green');
}

// Handle errors
process.on('unhandledRejection', (error) => {
  log(`❌ Unhandled error: ${error.message}`, 'red');
  process.exit(1);
});

process.on('SIGINT', () => {
  log('\n👋 Setup cancelled by user', 'yellow');
  process.exit(0);
});

// Run setup
main().catch((error) => {
  log(`❌ Setup failed: ${error.message}`, 'red');
  process.exit(1);
});
