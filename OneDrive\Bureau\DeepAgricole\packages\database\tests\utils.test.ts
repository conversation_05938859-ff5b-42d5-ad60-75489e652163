import {
  calculateBoundingBox,
  calculatePolygonArea,
  pointInPolygon,
  validateCoordinates,
  validatePolygon,
  calculateGrowingDegreeDays,
  calculateEvapotranspiration,
  calculateIrrigationNeed,
  calculateNutrientIndex,
  getDateRange,
} from '../src/utils';

describe('Geospatial Utilities', () => {
  describe('calculateBoundingBox', () => {
    it('should calculate correct bounding box for a polygon', () => {
      const coordinates = [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]];
      const bbox = calculateBoundingBox(coordinates);
      
      expect(bbox).toEqual({
        north: 1,
        south: 0,
        east: 1,
        west: 0,
      });
    });

    it('should handle negative coordinates', () => {
      const coordinates = [[[-1, -1], [1, -1], [1, 1], [-1, 1], [-1, -1]]];
      const bbox = calculateBoundingBox(coordinates);
      
      expect(bbox).toEqual({
        north: 1,
        south: -1,
        east: 1,
        west: -1,
      });
    });
  });

  describe('calculatePolygonArea', () => {
    it('should calculate area of a square polygon', () => {
      const coordinates = [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]];
      const area = calculatePolygonArea(coordinates);
      
      expect(area).toBe(1);
    });

    it('should calculate area of a triangle', () => {
      const coordinates = [[[0, 0], [2, 0], [1, 2], [0, 0]]];
      const area = calculatePolygonArea(coordinates);
      
      expect(area).toBe(2);
    });
  });

  describe('pointInPolygon', () => {
    const squarePolygon = [[[0, 0], [2, 0], [2, 2], [0, 2], [0, 0]]];

    it('should return true for point inside polygon', () => {
      expect(pointInPolygon([1, 1], squarePolygon)).toBe(true);
    });

    it('should return false for point outside polygon', () => {
      expect(pointInPolygon([3, 3], squarePolygon)).toBe(false);
    });

    it('should handle edge cases', () => {
      expect(pointInPolygon([0, 1], squarePolygon)).toBe(true); // On edge
      expect(pointInPolygon([0, 0], squarePolygon)).toBe(true); // On vertex
    });
  });

  describe('validateCoordinates', () => {
    it('should validate correct coordinates', () => {
      expect(validateCoordinates(45.0, 2.0)).toBe(true);
      expect(validateCoordinates(-90, -180)).toBe(true);
      expect(validateCoordinates(90, 180)).toBe(true);
    });

    it('should reject invalid coordinates', () => {
      expect(validateCoordinates(91, 0)).toBe(false);
      expect(validateCoordinates(-91, 0)).toBe(false);
      expect(validateCoordinates(0, 181)).toBe(false);
      expect(validateCoordinates(0, -181)).toBe(false);
    });
  });

  describe('validatePolygon', () => {
    it('should validate correct polygon', () => {
      const validPolygon = [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]];
      expect(validatePolygon(validPolygon)).toBe(true);
    });

    it('should reject unclosed polygon', () => {
      const unclosedPolygon = [[[0, 0], [1, 0], [1, 1], [0, 1]]];
      expect(validatePolygon(unclosedPolygon)).toBe(false);
    });

    it('should reject polygon with too few points', () => {
      const tooFewPoints = [[[0, 0], [1, 0], [0, 0]]];
      expect(validatePolygon(tooFewPoints)).toBe(false);
    });

    it('should reject polygon with invalid coordinates', () => {
      const invalidCoords = [[[0, 0], [1, 0], [1, 91], [0, 1], [0, 0]]];
      expect(validatePolygon(invalidCoords)).toBe(false);
    });
  });
});

describe('Agriculture Calculations', () => {
  describe('calculateGrowingDegreeDays', () => {
    it('should calculate GDD correctly', () => {
      const gdd = calculateGrowingDegreeDays(10, 25, 15);
      expect(gdd).toBe(10); // (25 + 15) / 2 - 10 = 10
    });

    it('should return 0 for temperatures below base', () => {
      const gdd = calculateGrowingDegreeDays(20, 15, 10);
      expect(gdd).toBe(0); // (15 + 10) / 2 - 20 = -7.5, but min is 0
    });
  });

  describe('calculateEvapotranspiration', () => {
    it('should calculate ET0 for typical conditions', () => {
      const et0 = calculateEvapotranspiration(25, 60, 2, 500);
      expect(et0).toBeGreaterThan(0);
      expect(et0).toBeLessThan(20); // Reasonable range for daily ET0
    });

    it('should return 0 for extreme conditions', () => {
      const et0 = calculateEvapotranspiration(-10, 100, 0, 0);
      expect(et0).toBe(0);
    });
  });

  describe('calculateIrrigationNeed', () => {
    it('should calculate irrigation need correctly', () => {
      const waterNeed = calculateIrrigationNeed(30, 50, 200, 1); // 1 hectare
      expect(waterNeed).toBe(400000); // 20% deficit * 200mm * 1ha * 10 = 400,000L
    });

    it('should return 0 when no irrigation needed', () => {
      const waterNeed = calculateIrrigationNeed(60, 50, 200, 1);
      expect(waterNeed).toBe(0);
    });
  });

  describe('calculateNutrientIndex', () => {
    it('should classify nutrients correctly', () => {
      const indices = calculateNutrientIndex(15, 10, 80);
      expect(indices).toEqual({
        nIndex: 'low',
        pIndex: 'low',
        kIndex: 'low',
      });
    });

    it('should handle high nutrient levels', () => {
      const indices = calculateNutrientIndex(50, 40, 250);
      expect(indices).toEqual({
        nIndex: 'high',
        pIndex: 'high',
        kIndex: 'high',
      });
    });

    it('should handle medium nutrient levels', () => {
      const indices = calculateNutrientIndex(30, 20, 150);
      expect(indices).toEqual({
        nIndex: 'medium',
        pIndex: 'medium',
        kIndex: 'medium',
      });
    });
  });
});

describe('Date Utilities', () => {
  describe('getDateRange', () => {
    const mockNow = new Date('2024-06-15T12:00:00Z');
    
    beforeAll(() => {
      jest.useFakeTimers();
      jest.setSystemTime(mockNow);
    });

    afterAll(() => {
      jest.useRealTimers();
    });

    it('should return correct day range', () => {
      const { from, to } = getDateRange('day');
      expect(to.getTime()).toBe(mockNow.getTime());
      expect(from.getDate()).toBe(mockNow.getDate() - 1);
    });

    it('should return correct week range', () => {
      const { from, to } = getDateRange('week');
      expect(to.getTime()).toBe(mockNow.getTime());
      expect(from.getDate()).toBe(mockNow.getDate() - 7);
    });

    it('should return correct month range', () => {
      const { from, to } = getDateRange('month');
      expect(to.getTime()).toBe(mockNow.getTime());
      expect(from.getMonth()).toBe(mockNow.getMonth() - 1);
    });

    it('should return correct year range', () => {
      const { from, to } = getDateRange('year');
      expect(to.getTime()).toBe(mockNow.getTime());
      expect(from.getFullYear()).toBe(mockNow.getFullYear() - 1);
    });
  });
});

describe('Error Handling', () => {
  it('should handle division by zero gracefully', () => {
    expect(() => calculatePolygonArea([[[0, 0], [0, 0], [0, 0], [0, 0]]])).not.toThrow();
  });

  it('should handle empty arrays gracefully', () => {
    expect(() => calculateBoundingBox([[]])).not.toThrow();
  });
});
