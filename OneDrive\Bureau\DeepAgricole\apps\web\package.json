{"name": "@aigricole/web", "version": "1.0.0", "description": "AIgricole Web Dashboard - Next.js 14 application for agricultural management", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:e2e": "playwright test", "test:coverage": "jest --coverage", "clean": "rm -rf .next dist coverage", "analyze": "ANALYZE=true npm run build"}, "dependencies": {"@aigricole/database": "workspace:*", "@aigricole/shared-ui": "workspace:*", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@next/bundle-analyzer": "^14.0.3", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.8.4", "@tanstack/react-table": "^8.10.7", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.5", "leaflet": "^1.9.4", "lucide-react": "^0.294.0", "next": "^14.0.3", "next-auth": "^4.24.5", "next-pwa": "^5.6.0", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-leaflet": "^4.2.1", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@next/eslint-config-next": "^14.0.3", "@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/leaflet": "^1.9.8", "@types/node": "^20.10.0", "@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.6", "typescript": "^5.3.2"}, "keywords": ["agriculture", "nextjs", "dashboard", "farming", "precision-agriculture", "iot", "ai", "crop-monitoring"], "author": "AIgricole Team", "license": "MIT"}