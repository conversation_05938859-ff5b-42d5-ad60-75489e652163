import { test as base } from '@playwright/test';

// Test data types
export interface TestUser {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: 'FARMER' | 'ADMIN' | 'AGRONOMIST';
}

export interface TestFarm {
  name: string;
  description: string;
  address: string;
  latitude: number;
  longitude: number;
  totalArea: number;
}

export interface TestField {
  name: string;
  description: string;
  area: number;
  soilType: 'CLAY' | 'SANDY' | 'LOAMY' | 'SILTY' | 'PEATY' | 'CHALKY';
  slope: number;
  elevation: number;
}

export interface TestCrop {
  name: string;
  variety: string;
  cropType: 'CEREALS' | 'VEGETABLES' | 'FRUITS' | 'LEGUMES' | 'OILSEEDS' | 'FORAGE';
  plantingDate: string;
  expectedHarvestDate: string;
  expectedYield: number;
}

export interface TestSensor {
  name: string;
  type: 'SOIL_MOISTURE' | 'SOIL_TEMPERATURE' | 'AIR_TEMPERATURE' | 'HUMIDITY' | 'LIGHT' | 'PH';
  model: string;
  serialNumber: string;
  latitude: number;
  longitude: number;
  measurementInterval: number;
}

// Test data fixtures
export const testUsers: TestUser[] = [
  {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    firstName: 'Jean',
    lastName: 'Dupont',
    role: 'FARMER',
  },
  {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    firstName: 'Marie',
    lastName: 'Martin',
    role: 'ADMIN',
  },
  {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    firstName: 'Pierre',
    lastName: 'Durand',
    role: 'AGRONOMIST',
  },
];

export const testFarms: TestFarm[] = [
  {
    name: 'Ferme des Champs Verts',
    description: 'Exploitation céréalière en agriculture biologique',
    address: '123 Route des Champs, 45000 Orléans, France',
    latitude: 47.9029,
    longitude: 1.9039,
    totalArea: 150.5,
  },
  {
    name: 'Domaine du Soleil',
    description: 'Maraîchage et arboriculture',
    address: '456 Chemin du Soleil, 84000 Avignon, France',
    latitude: 43.9493,
    longitude: 4.8055,
    totalArea: 75.2,
  },
];

export const testFields: TestField[] = [
  {
    name: 'Parcelle Nord',
    description: 'Champ de blé principal',
    area: 25.5,
    soilType: 'LOAMY',
    slope: 2.5,
    elevation: 120,
  },
  {
    name: 'Parcelle Sud',
    description: 'Champ de maïs',
    area: 18.3,
    soilType: 'CLAY',
    slope: 1.8,
    elevation: 115,
  },
  {
    name: 'Serre 1',
    description: 'Serre de tomates',
    area: 0.5,
    soilType: 'SANDY',
    slope: 0,
    elevation: 100,
  },
];

export const testCrops: TestCrop[] = [
  {
    name: 'Blé tendre',
    variety: 'Apache',
    cropType: 'CEREALS',
    plantingDate: '2024-10-15',
    expectedHarvestDate: '2025-07-20',
    expectedYield: 8.5,
  },
  {
    name: 'Maïs grain',
    variety: 'DKC 4795',
    cropType: 'CEREALS',
    plantingDate: '2024-04-20',
    expectedHarvestDate: '2024-10-15',
    expectedYield: 12.2,
  },
  {
    name: 'Tomates cerises',
    variety: 'Sweet 100',
    cropType: 'VEGETABLES',
    plantingDate: '2024-03-15',
    expectedHarvestDate: '2024-09-30',
    expectedYield: 45.0,
  },
];

export const testSensors: TestSensor[] = [
  {
    name: 'Capteur Humidité Sol 1',
    type: 'SOIL_MOISTURE',
    model: 'SoilWatch Pro',
    serialNumber: 'SW001234',
    latitude: 47.9030,
    longitude: 1.9040,
    measurementInterval: 300,
  },
  {
    name: 'Station Météo Principale',
    type: 'AIR_TEMPERATURE',
    model: 'WeatherStation 2000',
    serialNumber: 'WS005678',
    latitude: 47.9025,
    longitude: 1.9035,
    measurementInterval: 600,
  },
];

// Test scenarios
export const testScenarios = {
  // Authentication scenarios
  validLogin: {
    email: testUsers[0].email,
    password: testUsers[0].password,
  },
  invalidLogin: {
    email: '<EMAIL>',
    password: 'wrongpassword',
  },
  
  // Farm management scenarios
  createFarm: testFarms[0],
  updateFarm: {
    ...testFarms[0],
    name: 'Ferme des Champs Verts - Modifiée',
    totalArea: 160.0,
  },
  
  // Field management scenarios
  createField: testFields[0],
  updateField: {
    ...testFields[0],
    area: 30.0,
    description: 'Champ de blé principal - Étendu',
  },
  
  // Crop management scenarios
  createCrop: testCrops[0],
  updateCrop: {
    ...testCrops[0],
    expectedYield: 9.2,
  },
  
  // Sensor management scenarios
  createSensor: testSensors[0],
  updateSensor: {
    ...testSensors[0],
    measurementInterval: 600,
  },
  
  // Search scenarios
  searchQueries: [
    'blé',
    'tomate',
    'capteur',
    'humidité',
    'parcelle',
  ],
  
  // Filter scenarios
  filters: {
    cropType: 'CEREALS',
    soilType: 'LOAMY',
    sensorType: 'SOIL_MOISTURE',
    dateRange: {
      from: '2024-01-01',
      to: '2024-12-31',
    },
  },
};

// Mock API responses
export const mockApiResponses = {
  weatherData: {
    temperature: 22.5,
    humidity: 65,
    windSpeed: 12,
    precipitation: 0,
    condition: 'sunny',
    forecast: [
      { date: '2024-06-16', temperature: 24, condition: 'sunny' },
      { date: '2024-06-17', temperature: 21, condition: 'cloudy' },
      { date: '2024-06-18', temperature: 19, condition: 'rainy' },
    ],
  },
  
  sensorReadings: [
    {
      timestamp: '2024-06-15T12:00:00Z',
      value: 45.2,
      unit: '%',
      quality: 'GOOD',
    },
    {
      timestamp: '2024-06-15T12:05:00Z',
      value: 44.8,
      unit: '%',
      quality: 'GOOD',
    },
  ],
  
  cropHealth: {
    score: 85,
    status: 'HEALTHY',
    issues: [],
    recommendations: [
      'Continuer l\'irrigation actuelle',
      'Surveiller l\'apparition de maladies',
    ],
  },
  
  yieldPrediction: {
    predicted: 8.7,
    confidence: 0.92,
    factors: [
      { name: 'Météo', impact: 0.15 },
      { name: 'Sol', impact: 0.25 },
      { name: 'Variété', impact: 0.35 },
    ],
  },
};

// Test fixtures type
export type TestFixtures = {
  testUser: TestUser;
  testFarm: TestFarm;
  testField: TestField;
  testCrop: TestCrop;
  testSensor: TestSensor;
  authenticatedPage: any; // Playwright Page with authenticated user
};

// Extend Playwright test with fixtures
export const test = base.extend<TestFixtures>({
  testUser: async ({}, use) => {
    await use(testUsers[0]);
  },
  
  testFarm: async ({}, use) => {
    await use(testFarms[0]);
  },
  
  testField: async ({}, use) => {
    await use(testFields[0]);
  },
  
  testCrop: async ({}, use) => {
    await use(testCrops[0]);
  },
  
  testSensor: async ({}, use) => {
    await use(testSensors[0]);
  },
  
  authenticatedPage: async ({ page, testUser }, use) => {
    // Login with test user
    await page.goto('/auth/login');
    await page.fill('[data-testid="email-input"]', testUser.email);
    await page.fill('[data-testid="password-input"]', testUser.password);
    await page.click('[data-testid="login-button"]');
    
    // Wait for redirect to dashboard
    await page.waitForURL('/dashboard/**');
    
    await use(page);
  },
});

export { expect } from '@playwright/test';
