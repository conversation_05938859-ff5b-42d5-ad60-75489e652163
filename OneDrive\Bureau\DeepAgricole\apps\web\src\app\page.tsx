import { Suspense } from 'react';
import Link from 'next/link';
import { <PERSON>R<PERSON>, Leaf, Cpu, Satellite, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { HeroSection } from '@/components/sections/hero-section';
import { FeaturesSection } from '@/components/sections/features-section';
import { StatsSection } from '@/components/sections/stats-section';
import { TestimonialsSection } from '@/components/sections/testimonials-section';
import { CTASection } from '@/components/sections/cta-section';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

export default function HomePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Navigation */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center space-x-2">
            <Leaf className="h-8 w-8 text-primary" />
            <span className="text-2xl font-bold text-gradient">AIgricole</span>
          </div>
          
          <nav className="hidden md:flex items-center space-x-6">
            <Link href="#features" className="text-sm font-medium hover:text-primary transition-colors">
              Fonctionnalités
            </Link>
            <Link href="#solutions" className="text-sm font-medium hover:text-primary transition-colors">
              Solutions
            </Link>
            <Link href="#pricing" className="text-sm font-medium hover:text-primary transition-colors">
              Tarifs
            </Link>
            <Link href="#contact" className="text-sm font-medium hover:text-primary transition-colors">
              Contact
            </Link>
          </nav>
          
          <div className="flex items-center space-x-4">
            <Button variant="ghost" asChild>
              <Link href="/auth/login">Connexion</Link>
            </Button>
            <Button asChild>
              <Link href="/auth/register">
                Commencer
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </header>

      <main className="flex-1">
        {/* Hero Section */}
        <Suspense fallback={<LoadingSpinner />}>
          <HeroSection />
        </Suspense>

        {/* Features Overview */}
        <section className="py-24 bg-gray-50/50">
          <div className="container">
            <div className="text-center mb-16">
              <Badge variant="secondary" className="mb-4">
                Intelligence Artificielle Agricole
              </Badge>
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
                Révolutionnez votre agriculture
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Découvrez comment l'IA peut transformer votre exploitation agricole avec des solutions 
                innovantes pour optimiser vos rendements et réduire vos coûts.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <Card className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                    <Cpu className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle className="text-xl">IA Avancée</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Détection automatique des maladies, prédiction des rendements et recommandations personnalisées
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                    <Satellite className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle className="text-xl">Imagerie Satellite</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Surveillance en temps réel de vos parcelles avec analyse NDVI et détection des anomalies
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                    <Zap className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle className="text-xl">IoT Intelligent</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Capteurs connectés pour surveiller sol, météo et cultures avec alertes automatiques
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                    <Leaf className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle className="text-xl">Offline-First</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Fonctionnement complet hors ligne avec synchronisation intelligente pour le terrain
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Detailed Features */}
        <Suspense fallback={<LoadingSpinner />}>
          <FeaturesSection />
        </Suspense>

        {/* Statistics */}
        <Suspense fallback={<LoadingSpinner />}>
          <StatsSection />
        </Suspense>

        {/* Solutions by Farm Type */}
        <section id="solutions" className="py-24">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
                Solutions adaptées à votre exploitation
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Que vous soyez céréalier, maraîcher ou éleveur, AIgricole s'adapte à vos besoins spécifiques
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="text-2xl">Grandes Cultures</CardTitle>
                  <CardDescription>
                    Céréales, oléagineux, protéagineux
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Prédiction de rendement par parcelle
                    </li>
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Optimisation des intrants NPK
                    </li>
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Détection précoce des maladies
                    </li>
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Planning optimal des interventions
                    </li>
                  </ul>
                  <Button className="w-full" variant="outline">
                    En savoir plus
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow border-primary/20">
                <CardHeader>
                  <Badge className="w-fit mb-2">Populaire</Badge>
                  <CardTitle className="text-2xl">Maraîchage</CardTitle>
                  <CardDescription>
                    Légumes, fruits, cultures spécialisées
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Irrigation de précision automatisée
                    </li>
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Surveillance qualité des fruits
                    </li>
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Gestion des serres connectées
                    </li>
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Traçabilité complète des produits
                    </li>
                  </ul>
                  <Button className="w-full">
                    Commencer l'essai gratuit
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="text-2xl">Élevage</CardTitle>
                  <CardDescription>
                    Bovins, ovins, volailles
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Surveillance santé du cheptel
                    </li>
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Optimisation de l'alimentation
                    </li>
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Gestion des pâturages
                    </li>
                    <li className="flex items-center">
                      <ArrowRight className="h-4 w-4 text-primary mr-2" />
                      Prédiction de reproduction
                    </li>
                  </ul>
                  <Button className="w-full" variant="outline">
                    En savoir plus
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Testimonials */}
        <Suspense fallback={<LoadingSpinner />}>
          <TestimonialsSection />
        </Suspense>

        {/* CTA Section */}
        <Suspense fallback={<LoadingSpinner />}>
          <CTASection />
        </Suspense>
      </main>

      {/* Footer */}
      <footer className="border-t bg-background">
        <div className="container py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Leaf className="h-6 w-6 text-primary" />
                <span className="text-xl font-bold">AIgricole</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Révolutionnez votre agriculture avec l'intelligence artificielle
              </p>
            </div>
            
            <div className="space-y-4">
              <h4 className="text-sm font-semibold">Produit</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li><Link href="#" className="hover:text-primary">Fonctionnalités</Link></li>
                <li><Link href="#" className="hover:text-primary">Tarifs</Link></li>
                <li><Link href="#" className="hover:text-primary">API</Link></li>
                <li><Link href="#" className="hover:text-primary">Documentation</Link></li>
              </ul>
            </div>
            
            <div className="space-y-4">
              <h4 className="text-sm font-semibold">Support</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li><Link href="#" className="hover:text-primary">Centre d'aide</Link></li>
                <li><Link href="#" className="hover:text-primary">Contact</Link></li>
                <li><Link href="#" className="hover:text-primary">Formation</Link></li>
                <li><Link href="#" className="hover:text-primary">Communauté</Link></li>
              </ul>
            </div>
            
            <div className="space-y-4">
              <h4 className="text-sm font-semibold">Entreprise</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li><Link href="#" className="hover:text-primary">À propos</Link></li>
                <li><Link href="#" className="hover:text-primary">Blog</Link></li>
                <li><Link href="#" className="hover:text-primary">Carrières</Link></li>
                <li><Link href="#" className="hover:text-primary">Presse</Link></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t mt-8 pt-8 flex flex-col sm:flex-row justify-between items-center">
            <p className="text-sm text-muted-foreground">
              © 2024 AIgricole. Tous droits réservés.
            </p>
            <div className="flex space-x-4 mt-4 sm:mt-0">
              <Link href="#" className="text-sm text-muted-foreground hover:text-primary">
                Confidentialité
              </Link>
              <Link href="#" className="text-sm text-muted-foreground hover:text-primary">
                Conditions
              </Link>
              <Link href="#" className="text-sm text-muted-foreground hover:text-primary">
                Cookies
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
