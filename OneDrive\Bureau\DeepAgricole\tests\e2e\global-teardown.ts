import { FullConfig } from '@playwright/test';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Cleaning up E2E test environment...');

  // Clean up test database
  await cleanupTestDatabase();
  
  // Clean up test files
  cleanupTestFiles();
  
  // Generate test report summary
  generateTestSummary();
  
  console.log('✅ E2E test environment cleanup complete!');
}

async function cleanupTestDatabase() {
  console.log('🗄️  Cleaning up test database...');
  
  try {
    // Only clean up if we're not in CI or if explicitly requested
    const shouldCleanup = process.env.CLEANUP_TEST_DB === 'true' || !process.env.CI;
    
    if (shouldCleanup) {
      // Drop test database
      try {
        execSync('dropdb aigricole_test', { stdio: 'ignore' });
        console.log('✅ Test database dropped');
      } catch (error) {
        console.warn('⚠️  Failed to drop test database (might not exist)');
      }
    } else {
      console.log('ℹ️  Skipping test database cleanup (CI environment)');
    }
  } catch (error) {
    console.error('❌ Failed to cleanup test database:', error);
  }
}

function cleanupTestFiles() {
  console.log('📁 Cleaning up test files...');
  
  try {
    // Clean up temporary test files
    const tempPaths = [
      path.join(__dirname, 'fixtures', 'temp'),
      path.join(__dirname, 'downloads'),
      path.join(__dirname, 'uploads'),
    ];
    
    tempPaths.forEach(tempPath => {
      if (fs.existsSync(tempPath)) {
        fs.rmSync(tempPath, { recursive: true, force: true });
        console.log(`✅ Cleaned up: ${tempPath}`);
      }
    });
    
    // Clean up test user data file if it exists
    const testUsersPath = path.join(__dirname, 'fixtures', 'test-users.json');
    if (fs.existsSync(testUsersPath)) {
      fs.unlinkSync(testUsersPath);
      console.log('✅ Cleaned up test users file');
    }
    
  } catch (error) {
    console.error('❌ Failed to cleanup test files:', error);
  }
}

function generateTestSummary() {
  console.log('📊 Generating test summary...');
  
  try {
    const resultsPath = path.join(process.cwd(), 'test-results', 'results.json');
    
    if (fs.existsSync(resultsPath)) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      
      const summary = {
        timestamp: new Date().toISOString(),
        total: results.stats?.total || 0,
        passed: results.stats?.passed || 0,
        failed: results.stats?.failed || 0,
        skipped: results.stats?.skipped || 0,
        duration: results.stats?.duration || 0,
        projects: results.config?.projects?.map((p: any) => p.name) || [],
      };
      
      console.log('\n📈 Test Summary:');
      console.log(`   Total Tests: ${summary.total}`);
      console.log(`   Passed: ${summary.passed}`);
      console.log(`   Failed: ${summary.failed}`);
      console.log(`   Skipped: ${summary.skipped}`);
      console.log(`   Duration: ${Math.round(summary.duration / 1000)}s`);
      console.log(`   Projects: ${summary.projects.join(', ')}`);
      
      // Save summary for CI/CD
      const summaryPath = path.join(process.cwd(), 'test-results', 'summary.json');
      fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
      
      // Generate badge data for README
      const badgeData = {
        schemaVersion: 1,
        label: 'e2e tests',
        message: summary.failed > 0 ? `${summary.failed} failing` : `${summary.passed} passing`,
        color: summary.failed > 0 ? 'red' : 'green',
      };
      
      const badgePath = path.join(process.cwd(), 'test-results', 'badge.json');
      fs.writeFileSync(badgePath, JSON.stringify(badgeData, null, 2));
      
      console.log('✅ Test summary generated');
    } else {
      console.warn('⚠️  No test results found for summary generation');
    }
  } catch (error) {
    console.error('❌ Failed to generate test summary:', error);
  }
}

// Archive test artifacts for CI/CD
function archiveTestArtifacts() {
  console.log('📦 Archiving test artifacts...');
  
  try {
    const artifactPaths = [
      'test-results',
      'playwright-report',
      'coverage',
    ];
    
    const archiveDir = path.join(process.cwd(), 'artifacts');
    
    if (!fs.existsSync(archiveDir)) {
      fs.mkdirSync(archiveDir, { recursive: true });
    }
    
    artifactPaths.forEach(artifactPath => {
      if (fs.existsSync(artifactPath)) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const archivePath = path.join(archiveDir, `${artifactPath}-${timestamp}`);
        
        // Copy directory
        fs.cpSync(artifactPath, archivePath, { recursive: true });
        console.log(`✅ Archived: ${artifactPath} -> ${archivePath}`);
      }
    });
    
  } catch (error) {
    console.error('❌ Failed to archive test artifacts:', error);
  }
}

// Send test results to external services (if configured)
async function sendTestResults() {
  console.log('📤 Sending test results...');
  
  try {
    // Send to Slack (if webhook configured)
    if (process.env.SLACK_WEBHOOK_URL) {
      await sendSlackNotification();
    }
    
    // Send to Discord (if webhook configured)
    if (process.env.DISCORD_WEBHOOK_URL) {
      await sendDiscordNotification();
    }
    
    // Send to custom webhook (if configured)
    if (process.env.CUSTOM_WEBHOOK_URL) {
      await sendCustomWebhook();
    }
    
  } catch (error) {
    console.error('❌ Failed to send test results:', error);
  }
}

async function sendSlackNotification() {
  // Implementation for Slack notification
  console.log('📱 Slack notification sent');
}

async function sendDiscordNotification() {
  // Implementation for Discord notification
  console.log('🎮 Discord notification sent');
}

async function sendCustomWebhook() {
  // Implementation for custom webhook
  console.log('🔗 Custom webhook notification sent');
}

// Main teardown function
async function main(config: FullConfig) {
  try {
    await globalTeardown(config);
    
    // Archive artifacts if in CI
    if (process.env.CI) {
      archiveTestArtifacts();
    }
    
    // Send results if configured
    if (process.env.SEND_TEST_RESULTS === 'true') {
      await sendTestResults();
    }
    
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't exit with error code as this might mask test failures
  }
}

export default main;
